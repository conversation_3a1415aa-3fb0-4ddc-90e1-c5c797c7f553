{
    "m_SGVersion": 3,
    "m_Type": "UnityEditor.ShaderGraph.GraphData",
    "m_ObjectId": "0c0fb68778114e62b83d099efd6a5560",
    "m_Properties": [],
    "m_Keywords": [],
    "m_Dropdowns": [],
    "m_CategoryData": [
        {
            "m_Id": "0ed8affd0b2e4900b40f1fd3d9e67c01"
        }
    ],
    "m_Nodes": [
        {
            "m_Id": "1549c79363554810a204f77eb1c3e0df"
        },
        {
            "m_Id": "70d2891fcf6441078175ae15c3935f14"
        },
        {
            "m_Id": "4d2c13a7eb4648b58c30f81052575fa0"
        }
    ],
    "m_GroupDatas": [],
    "m_StickyNoteDatas": [
        {
            "m_Id": "35792eabbe6e4038b09e2f9ba32ea6df"
        }
    ],
    "m_Edges": [
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "4d2c13a7eb4648b58c30f81052575fa0"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "70d2891fcf6441078175ae15c3935f14"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "70d2891fcf6441078175ae15c3935f14"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "1549c79363554810a204f77eb1c3e0df"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "70d2891fcf6441078175ae15c3935f14"
                },
                "m_SlotId": 3
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "1549c79363554810a204f77eb1c3e0df"
                },
                "m_SlotId": 2
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "70d2891fcf6441078175ae15c3935f14"
                },
                "m_SlotId": 5
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "1549c79363554810a204f77eb1c3e0df"
                },
                "m_SlotId": 3
            }
        }
    ],
    "m_VertexContext": {
        "m_Position": {
            "x": 0.0,
            "y": 0.0
        },
        "m_Blocks": []
    },
    "m_FragmentContext": {
        "m_Position": {
            "x": 0.0,
            "y": 0.0
        },
        "m_Blocks": []
    },
    "m_PreviewData": {
        "serializedMesh": {
            "m_SerializedMesh": "{\"mesh\":{\"instanceID\":0}}",
            "m_Guid": ""
        },
        "preventRotation": false
    },
    "m_Path": "Sub Graphs",
    "m_GraphPrecision": 1,
    "m_PreviewMode": 2,
    "m_OutputNode": {
        "m_Id": "1549c79363554810a204f77eb1c3e0df"
    },
    "m_SubDatas": [],
    "m_ActiveTargets": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "09cc1cc7893840c0b3637bb51d47645b",
    "m_Id": 3,
    "m_DisplayName": "ShadowAtten",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "ShadowAtten",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.CategoryData",
    "m_ObjectId": "0ed8affd0b2e4900b40f1fd3d9e67c01",
    "m_Name": "",
    "m_ChildObjectList": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "0f87204c3df9471080806c1d4ad538f7",
    "m_Id": 1,
    "m_DisplayName": "Direction",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Direction",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SubGraphOutputNode",
    "m_ObjectId": "1549c79363554810a204f77eb1c3e0df",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Output",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "0f87204c3df9471080806c1d4ad538f7"
        },
        {
            "m_Id": "d8368764d24a40dcbd978c1378a7260b"
        },
        {
            "m_Id": "09cc1cc7893840c0b3637bb51d47645b"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "IsFirstSlotValid": true
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "35792eabbe6e4038b09e2f9ba32ea6df",
    "m_Title": "",
    "m_Content": "Using this Custom Function node, we're able to call URP code functions directly in order to get the lighting data that isn't otherwise available in Shader Graph.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -252.0,
        "y": 146.0,
        "width": 216.0,
        "height": 100.0
    },
    "m_Group": {
        "m_Id": ""
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "3e799ddb37634f2b8cc3586c711ffeae",
    "m_Id": 0,
    "m_DisplayName": "worldPos",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "worldPos",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.PositionNode",
    "m_ObjectId": "4d2c13a7eb4648b58c30f81052575fa0",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Position",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -459.0,
            "y": 0.0,
            "width": 206.0,
            "height": 131.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "91e62c2f418a4b55a0ea4bf1afca2e2b"
        }
    ],
    "synonyms": [
        "location"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 2,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Space": 4,
    "m_PositionSource": 0
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.CustomFunctionNode",
    "m_ObjectId": "70d2891fcf6441078175ae15c3935f14",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "MainLight (Custom Function)",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -253.0,
            "y": 0.0,
            "width": 217.5,
            "height": 142.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "3e799ddb37634f2b8cc3586c711ffeae"
        },
        {
            "m_Id": "e5d5d5fd021143418488129ad44dd94c"
        },
        {
            "m_Id": "e26b6fea1aea48f5854ae9d1c02d9304"
        },
        {
            "m_Id": "c6c41782f0644d469b2b503a020f22c9"
        }
    ],
    "synonyms": [
        "code",
        "HLSL"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SourceType": 1,
    "m_FunctionName": "MainLight",
    "m_FunctionSource": "3beadf505dbc54f4cae878435013d751",
    "m_FunctionBody": "#ifdef SHADERGRAPH_PREVIEW\n    direction = normalize(float3(-0.5,0.5,-0.5));\n    color = float3(1,1,1);\n    shadowAtten = 1;\n#else\n    #if defined(UNIVERSAL_PIPELINE_CORE_INCLUDED)\n        float4 shadowCoord = TransformWorldToShadowCoord(worldPos);\n        Light mainLight = GetMainLight(shadowCoord);\n        direction = mainLight.direction;\n        color = mainLight.color;\n        shadowAtten = mainLight.shadowAttenuation;\n    #else\n        direction = normalize(float3(-0.5, 0.5, -0.5));\n        color = float3(1, 1, 1);\n        shadowAtten = 1;\n    #endif\n#endif"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "91e62c2f418a4b55a0ea4bf1afca2e2b",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "c6c41782f0644d469b2b503a020f22c9",
    "m_Id": 5,
    "m_DisplayName": "shadowAtten",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "shadowAtten",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "d8368764d24a40dcbd978c1378a7260b",
    "m_Id": 2,
    "m_DisplayName": "Color",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Color",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "e26b6fea1aea48f5854ae9d1c02d9304",
    "m_Id": 3,
    "m_DisplayName": "color",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "color",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "e5d5d5fd021143418488129ad44dd94c",
    "m_Id": 2,
    "m_DisplayName": "direction",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "direction",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

