using UnityEngine;
using UnityEngine.UI;
using UnityEngine.SceneManagement;
using TMPro;
using System.Collections;

public class MainMenuManager : MonoBehaviour
{
    [Header("UI Elements")]
    public TextMeshProUGUI gameTitle;
    public Button playButton;
    public Button optionsButton;
    public Button quitButton;
    
    [<PERSON><PERSON>("Animation Settings")]
    public float fadeInDuration = 1.0f;
    public float buttonAnimationDelay = 0.2f;
    public float titleAnimationDelay = 0.5f;
    
    [Header("Scene Management")]
    public string gameSceneName = "GameScene";
    public string optionsSceneName = "OptionsScene";
    
    private CanvasGroup canvasGroup;
    
    void Start()
    {
        InitializeMenu();
        StartCoroutine(AnimateMenuEntrance());
    }
    
    void InitializeMenu()
    {
        // Get or add CanvasGroup for fade animations
        canvasGroup = GetComponent<CanvasGroup>();
        if (canvasGroup == null)
        {
            canvasGroup = gameObject.AddComponent<CanvasGroup>();
        }
        
        // Set initial alpha to 0 for fade-in effect
        canvasGroup.alpha = 0f;
        
        // Setup button listeners
        if (playButton != null)
            playButton.onClick.AddListener(OnPlayButtonClicked);
        
        if (optionsButton != null)
            optionsButton.onClick.AddListener(OnOptionsButtonClicked);
        
        if (quitButton != null)
            quitButton.onClick.AddListener(OnQuitButtonClicked);
    }
    
    IEnumerator AnimateMenuEntrance()
    {
        // Wait for title animation delay
        yield return new WaitForSeconds(titleAnimationDelay);
        
        // Fade in the entire menu
        float elapsedTime = 0f;
        while (elapsedTime < fadeInDuration)
        {
            elapsedTime += Time.deltaTime;
            canvasGroup.alpha = Mathf.Lerp(0f, 1f, elapsedTime / fadeInDuration);
            yield return null;
        }
        canvasGroup.alpha = 1f;
        
        // Animate buttons individually
        yield return StartCoroutine(AnimateButtons());
    }
    
    IEnumerator AnimateButtons()
    {
        Button[] buttons = { playButton, optionsButton, quitButton };
        
        foreach (Button button in buttons)
        {
            if (button != null)
            {
                StartCoroutine(AnimateButtonScale(button));
                yield return new WaitForSeconds(buttonAnimationDelay);
            }
        }
    }
    
    IEnumerator AnimateButtonScale(Button button)
    {
        Vector3 originalScale = button.transform.localScale;
        button.transform.localScale = Vector3.zero;
        
        float elapsedTime = 0f;
        float animationDuration = 0.3f;
        
        while (elapsedTime < animationDuration)
        {
            elapsedTime += Time.deltaTime;
            float scale = Mathf.Lerp(0f, 1f, elapsedTime / animationDuration);
            button.transform.localScale = originalScale * scale;
            yield return null;
        }
        
        button.transform.localScale = originalScale;
    }
    
    public void OnPlayButtonClicked()
    {
        Debug.Log("Play button clicked!");
        GameSceneManager.Instance.LoadGameScene();
    }

    public void OnOptionsButtonClicked()
    {
        Debug.Log("Options button clicked!");
        GameSceneManager.Instance.LoadOptionsScene();
    }

    public void OnQuitButtonClicked()
    {
        Debug.Log("Quit button clicked!");
        GameSceneManager.Instance.QuitGame();
    }
    

}
