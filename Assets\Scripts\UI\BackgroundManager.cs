using UnityEngine;
using UnityEngine.UI;
using System.Collections;

public class BackgroundManager : MonoBehaviour
{
    [Header("Background Settings")]
    public Color backgroundColor = new Color(0.1f, 0.1f, 0.2f, 1f);
    public Gradient backgroundGradient;
    public bool useGradient = true;
    
    [Header("Animated Background")]
    public bool animateBackground = true;
    public float animationSpeed = 1f;
    public float colorShiftIntensity = 0.1f;
    
    [Header("Particle Effects")]
    public bool enableParticles = true;
    public GameObject particleSystemPrefab;
    public int particleCount = 50;
    public float particleSpeed = 1f;
    
    [Header("Background Image")]
    public Sprite backgroundSprite;
    public bool useBackgroundImage = false;
    public float backgroundImageAlpha = 0.3f;
    
    private Camera mainCamera;
    private Image backgroundImage;
    private ParticleSystem backgroundParticles;
    private float animationTime = 0f;
    
    void Start()
    {
        mainCamera = Camera.main;
        if (mainCamera == null)
        {
            mainCamera = FindObjectOfType<Camera>();
        }
        
        SetupBackground();
        
        if (enableParticles)
        {
            SetupParticleSystem();
        }
    }
    
    void Update()
    {
        if (animateBackground)
        {
            AnimateBackground();
        }
    }
    
    void SetupBackground()
    {
        if (useBackgroundImage && backgroundSprite != null)
        {
            CreateBackgroundImage();
        }
        else
        {
            SetupCameraBackground();
        }
    }
    
    void CreateBackgroundImage()
    {
        // Create a background image GameObject
        GameObject bgObject = new GameObject("BackgroundImage");
        bgObject.transform.SetParent(transform, false);
        
        // Add Image component
        backgroundImage = bgObject.AddComponent<Image>();
        backgroundImage.sprite = backgroundSprite;
        backgroundImage.color = new Color(1f, 1f, 1f, backgroundImageAlpha);
        
        // Set up RectTransform to fill the screen
        RectTransform rectTransform = bgObject.GetComponent<RectTransform>();
        rectTransform.anchorMin = Vector2.zero;
        rectTransform.anchorMax = Vector2.one;
        rectTransform.sizeDelta = Vector2.zero;
        rectTransform.anchoredPosition = Vector2.zero;
        
        // Move to back
        bgObject.transform.SetAsFirstSibling();
    }
    
    void SetupCameraBackground()
    {
        if (mainCamera != null)
        {
            mainCamera.backgroundColor = backgroundColor;
        }
    }
    
    void AnimateBackground()
    {
        animationTime += Time.deltaTime * animationSpeed;
        
        if (useGradient && backgroundGradient != null)
        {
            // Animate through gradient
            float gradientTime = (Mathf.Sin(animationTime) + 1f) * 0.5f;
            Color currentColor = backgroundGradient.Evaluate(gradientTime);
            
            if (mainCamera != null)
            {
                mainCamera.backgroundColor = currentColor;
            }
        }
        else
        {
            // Simple color shifting
            Color baseColor = backgroundColor;
            float colorShift = Mathf.Sin(animationTime) * colorShiftIntensity;
            
            Color animatedColor = new Color(
                Mathf.Clamp01(baseColor.r + colorShift),
                Mathf.Clamp01(baseColor.g + colorShift * 0.5f),
                Mathf.Clamp01(baseColor.b + colorShift * 1.5f),
                baseColor.a
            );
            
            if (mainCamera != null)
            {
                mainCamera.backgroundColor = animatedColor;
            }
        }
        
        // Animate background image if present
        if (backgroundImage != null)
        {
            float alpha = backgroundImageAlpha + (Mathf.Sin(animationTime * 0.5f) * 0.1f);
            Color imageColor = backgroundImage.color;
            imageColor.a = Mathf.Clamp01(alpha);
            backgroundImage.color = imageColor;
        }
    }
    
    void SetupParticleSystem()
    {
        GameObject particleObject = new GameObject("BackgroundParticles");
        particleObject.transform.SetParent(transform, false);
        
        backgroundParticles = particleObject.AddComponent<ParticleSystem>();
        
        // Configure particle system
        var main = backgroundParticles.main;
        main.startLifetime = 10f;
        main.startSpeed = particleSpeed;
        main.startSize = 0.1f;
        main.startColor = new Color(1f, 1f, 1f, 0.3f);
        main.maxParticles = particleCount;
        
        var emission = backgroundParticles.emission;
        emission.rateOverTime = particleCount / 10f;
        
        var shape = backgroundParticles.shape;
        shape.enabled = true;
        shape.shapeType = ParticleSystemShapeType.Rectangle;
        shape.scale = new Vector3(20f, 15f, 1f);
        
        var velocityOverLifetime = backgroundParticles.velocityOverLifetime;
        velocityOverLifetime.enabled = true;
        velocityOverLifetime.space = ParticleSystemSimulationSpace.Local;
        velocityOverLifetime.linear = new ParticleSystem.MinMaxCurve(0.5f, new Vector3(0f, 1f, 0f));
        
        var colorOverLifetime = backgroundParticles.colorOverLifetime;
        colorOverLifetime.enabled = true;
        Gradient gradient = new Gradient();
        gradient.SetKeys(
            new GradientColorKey[] { 
                new GradientColorKey(Color.white, 0.0f), 
                new GradientColorKey(Color.white, 1.0f) 
            },
            new GradientAlphaKey[] { 
                new GradientAlphaKey(0.0f, 0.0f), 
                new GradientAlphaKey(0.3f, 0.5f), 
                new GradientAlphaKey(0.0f, 1.0f) 
            }
        );
        colorOverLifetime.color = gradient;
        
        // Position particle system behind UI
        particleObject.transform.position = new Vector3(0, 0, 5);
    }
    
    // Public methods for runtime control
    public void SetBackgroundColor(Color newColor)
    {
        backgroundColor = newColor;
        if (mainCamera != null)
        {
            mainCamera.backgroundColor = newColor;
        }
    }
    
    public void SetAnimationSpeed(float speed)
    {
        animationSpeed = speed;
    }
    
    public void ToggleAnimation(bool enable)
    {
        animateBackground = enable;
    }
    
    public void ToggleParticles(bool enable)
    {
        enableParticles = enable;
        if (backgroundParticles != null)
        {
            if (enable)
            {
                backgroundParticles.Play();
            }
            else
            {
                backgroundParticles.Stop();
            }
        }
    }
    
    // Create a simple gradient for background animation
    void CreateDefaultGradient()
    {
        backgroundGradient = new Gradient();
        
        GradientColorKey[] colorKeys = new GradientColorKey[3];
        colorKeys[0].color = new Color(0.2f, 0.1f, 0.3f);
        colorKeys[0].time = 0.0f;
        colorKeys[1].color = new Color(0.1f, 0.2f, 0.4f);
        colorKeys[1].time = 0.5f;
        colorKeys[2].color = new Color(0.3f, 0.1f, 0.2f);
        colorKeys[2].time = 1.0f;
        
        GradientAlphaKey[] alphaKeys = new GradientAlphaKey[2];
        alphaKeys[0].alpha = 1.0f;
        alphaKeys[0].time = 0.0f;
        alphaKeys[1].alpha = 1.0f;
        alphaKeys[1].time = 1.0f;
        
        backgroundGradient.SetKeys(colorKeys, alphaKeys);
    }
    
    void OnValidate()
    {
        if (backgroundGradient == null && useGradient)
        {
            CreateDefaultGradient();
        }
    }
}
