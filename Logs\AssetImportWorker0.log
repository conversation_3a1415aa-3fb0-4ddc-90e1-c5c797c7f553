Using pre-set license
Built from '6000.1/staging' branch; Version is '6000.1.15f1 (faa32412f6c9) revision 16425764'; Using compiler version '194234433'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit CoreSingleLanguage' Language: 'en' Physical Memory: 3834 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1
Date: 2025-08-08T05:51:50Z

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.1.15f1-x86_64\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
C:/xampp/htdocs/Gamepertama
-logFile
Logs/AssetImportWorker0.log
-srvPort
53986
-job-worker-count
1
-background-job-worker-count
8
-gc-helper-count
1
-name
AssetImport
Successfully changed project path to: C:/xampp/htdocs/Gamepertama
C:/xampp/htdocs/Gamepertama
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [18248]  Target information:

Player connection [18248]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 1559351928 [EditorId] 1559351928 [Version] 1048832 [Id] WindowsEditor(7,LAPTOP-1PJJO5TI) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [18248] Host joined multi-casting on [***********:54997]...
Player connection [18248] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 1
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 144.05 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.1.15f1 (faa32412f6c9)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.1.15f1-x86_64/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/xampp/htdocs/Gamepertama/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:         Direct3D 12 [level 12.1]
    Renderer:        Intel(R) UHD Graphics (ID=0x8a56)
    Vendor:          Intel
    VRAM:            1917 MB
    App VRAM Budget: 1725 MB
    Driver:          30.0.100.9864
    Unified Memory Architecture
    Cache Coherent UMA
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.1.15f1-x86_64/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.1.15f1-x86_64/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.1.15f1-x86_64/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56088
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.1.15f1-x86_64/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.014365 seconds.
- Loaded All Assemblies, in  2.484 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.609 seconds
Domain Reload Profiling: 4075ms
	BeginReloadAssembly (651ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (8ms)
	RebuildCommonClasses (138ms)
	RebuildNativeTypeToScriptingClass (29ms)
	initialDomainReloadingComplete (281ms)
	LoadAllAssembliesAndSetupDomain (1365ms)
		LoadAssemblies (643ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1350ms)
			TypeCache.Refresh (1346ms)
				TypeCache.ScanAssembly (1288ms)
			BuildScriptInfoCaches (1ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (1610ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1337ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (225ms)
			SetLoadedEditorAssemblies (78ms)
			BeforeProcessingInitializeOnLoad (269ms)
			ProcessInitializeOnLoadAttributes (510ms)
			ProcessInitializeOnLoadMethodAttributes (254ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  7.163 seconds
Refreshing native plugins compatible for Editor in 7.91 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in 154.527 seconds
Domain Reload Profiling: 161620ms
	BeginReloadAssembly (776ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (16ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (132ms)
	RebuildCommonClasses (186ms)
	RebuildNativeTypeToScriptingClass (37ms)
	initialDomainReloadingComplete (119ms)
	LoadAllAssembliesAndSetupDomain (5944ms)
		LoadAssemblies (2768ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (3633ms)
			TypeCache.Refresh (3025ms)
				TypeCache.ScanAssembly (2411ms)
			BuildScriptInfoCaches (469ms)
			ResolveRequiredComponents (99ms)
	FinalizeReload (154558ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (152439ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (25ms)
			BeforeProcessingInitializeOnLoad (1104ms)
			ProcessInitializeOnLoadAttributes (139689ms)
			ProcessInitializeOnLoadMethodAttributes (11239ms)
			AfterProcessingInitializeOnLoad (309ms)
			EditorAssembliesLoaded (51ms)
		ExecutionOrderSort2 (5ms)
		AwakeInstancesAfterBackupRestoration (342ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.97 seconds
Refreshing native plugins compatible for Editor in 19.95 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 32 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6617 unused Assets / (4.1 MB). Loaded Objects now: 7293.
Memory consumption went from 134.0 MB to 130.0 MB.
Total: 2207.838600 ms (FindLiveObjects: 923.124100 ms CreateObjectMapping: 3.262900 ms MarkObjects: 1204.644800 ms  DeleteObjects: 76.574500 ms)

========================================================================
Received Import Request.
  Time since last request: 16268.156073 seconds.
  path: Assets/InputSystem_Actions.inputactions
  artifactKey: Guid(2bcd2660ca9b64942af0de543d8d7100) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/InputSystem_Actions.inputactions using Guid(2bcd2660ca9b64942af0de543d8d7100) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4b247e7907b7d4a86176f4ae684b564d') in 4.5391613 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 0.000084 seconds.
  path: Assets/UniversalRenderPipelineGlobalSettings.asset
  artifactKey: Guid(93b439a37f63240aca3dd4e01d978a9f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UniversalRenderPipelineGlobalSettings.asset using Guid(93b439a37f63240aca3dd4e01d978a9f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e346db4b4726e88aa1fa0d28e5dff1a5') in 0.0047476 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000084 seconds.
  path: Assets/DefaultVolumeProfile.asset
  artifactKey: Guid(3f9215ea0144899419cfbc0957140d3f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/DefaultVolumeProfile.asset using Guid(3f9215ea0144899419cfbc0957140d3f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1e3ee85287093c2e01e2184a29fd3edf') in 0.0163739 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

