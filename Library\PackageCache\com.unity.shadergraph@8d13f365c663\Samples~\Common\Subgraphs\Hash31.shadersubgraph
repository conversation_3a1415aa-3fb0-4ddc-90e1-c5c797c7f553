{
    "m_SGVersion": 3,
    "m_Type": "UnityEditor.ShaderGraph.GraphData",
    "m_ObjectId": "a805d50baeec43e688c68613cba85f5b",
    "m_Properties": [
        {
            "m_Id": "2a63d0b0194144d6b8cb44184581a7bf"
        }
    ],
    "m_Keywords": [],
    "m_Dropdowns": [],
    "m_CategoryData": [
        {
            "m_Id": "e6ef3e8653e54ab4b528b830c517ec15"
        }
    ],
    "m_Nodes": [
        {
            "m_Id": "d47abf6eb8234e1c8deb90ae9dae0c62"
        },
        {
            "m_Id": "5679c699a8824f3f842ca904210e7529"
        },
        {
            "m_Id": "20799078114c4b6db04ea1100b7eef61"
        }
    ],
    "m_GroupDatas": [],
    "m_StickyNoteDatas": [
        {
            "m_Id": "a69b323dfd154f349735f42e3b3dd252"
        }
    ],
    "m_Edges": [
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "20799078114c4b6db04ea1100b7eef61"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "d47abf6eb8234e1c8deb90ae9dae0c62"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "5679c699a8824f3f842ca904210e7529"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "20799078114c4b6db04ea1100b7eef61"
                },
                "m_SlotId": 1
            }
        }
    ],
    "m_VertexContext": {
        "m_Position": {
            "x": 0.0,
            "y": 0.0
        },
        "m_Blocks": []
    },
    "m_FragmentContext": {
        "m_Position": {
            "x": 0.0,
            "y": 0.0
        },
        "m_Blocks": []
    },
    "m_PreviewData": {
        "serializedMesh": {
            "m_SerializedMesh": "{\"mesh\":{\"instanceID\":0}}",
            "m_Guid": ""
        },
        "preventRotation": false
    },
    "m_Path": "Procedural/Noise",
    "m_GraphPrecision": 1,
    "m_PreviewMode": 2,
    "m_OutputNode": {
        "m_Id": "d47abf6eb8234e1c8deb90ae9dae0c62"
    },
    "m_ActiveTargets": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "025d87ec83e84666a7c431c73acc20bd",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.CustomFunctionNode",
    "m_ObjectId": "20799078114c4b6db04ea1100b7eef61",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Tchou31 (Custom Function)",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -354.66668701171877,
            "y": 26.666627883911134,
            "width": 205.33334350585938,
            "height": 94.00003814697266
        }
    },
    "m_Slots": [
        {
            "m_Id": "d8a1e067c1194567ae4c7188c03d3e68"
        },
        {
            "m_Id": "025d87ec83e84666a7c431c73acc20bd"
        }
    ],
    "synonyms": [
        "code",
        "HLSL"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SourceType": 1,
    "m_FunctionName": "Tchou31",
    "m_FunctionSource": "",
    "m_FunctionBody": "//tchou31\nuint3 v = (uint3) (int3) round(p);\r\n    v.x ^= 1103515245U;\n    v.y ^= v.x + v.z;\n    v.y = v.y * 134775813;\n    v.z += v.x ^ v.y;\n    v.y += v.x ^ v.z;\n    v.x += v.y * v.z;\n    v.x = v.x * 0x27d4eb2du;\r\n\nOut = v.x * (1.0 / float(0xffffffff));"
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.Internal.Vector3ShaderProperty",
    "m_ObjectId": "2a63d0b0194144d6b8cb44184581a7bf",
    "m_Guid": {
        "m_GuidSerialized": "ca88e2c4-4926-4b5b-9ac9-8be19fbc9474"
    },
    "m_Name": "In",
    "m_DefaultRefNameVersion": 1,
    "m_RefNameGeneratedByDisplayName": "In",
    "m_DefaultReferenceName": "_In",
    "m_OverrideReferenceName": "",
    "m_GeneratePropertyBlock": true,
    "m_UseCustomSlotLabel": false,
    "m_CustomSlotLabel": "",
    "m_DismissedVersion": 0,
    "m_Precision": 0,
    "overrideHLSLDeclaration": false,
    "hlslDeclarationOverride": 0,
    "m_Hidden": false,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "2e8213bb9ffa4fafb85dcc321b34405a",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PropertyNode",
    "m_ObjectId": "5679c699a8824f3f842ca904210e7529",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Property",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -441.33349609375,
            "y": 70.66668701171875,
            "width": 86.66690063476563,
            "height": 34.00000762939453
        }
    },
    "m_Slots": [
        {
            "m_Id": "e65d414797654ad2a161727564dcb821"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Property": {
        "m_Id": "2a63d0b0194144d6b8cb44184581a7bf"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "a69b323dfd154f349735f42e3b3dd252",
    "m_Title": "",
    "m_Content": "Generates a random output value for every unique input value.\n\nThis one receives a Vec3 and outputs a Float.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -351.0000305175781,
        "y": -74.50000762939453,
        "width": 200.00001525878907,
        "height": 100.00001525878906
    },
    "m_Group": {
        "m_Id": ""
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SubGraphOutputNode",
    "m_ObjectId": "d47abf6eb8234e1c8deb90ae9dae0c62",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Output",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -148.00010681152345,
            "y": 27.999996185302736,
            "width": 85.33343505859375,
            "height": 76.66665649414063
        }
    },
    "m_Slots": [
        {
            "m_Id": "2e8213bb9ffa4fafb85dcc321b34405a"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "IsFirstSlotValid": true
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "d8a1e067c1194567ae4c7188c03d3e68",
    "m_Id": 1,
    "m_DisplayName": "p",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "p",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "e65d414797654ad2a161727564dcb821",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.CategoryData",
    "m_ObjectId": "e6ef3e8653e54ab4b528b830c517ec15",
    "m_Name": "",
    "m_ChildObjectList": [
        {
            "m_Id": "2a63d0b0194144d6b8cb44184581a7bf"
        }
    ]
}

