VisibilityToolWindow {
    flex:1 0;
    flex-direction: row-reverse;
    padding-bottom : 10px;
    width: 300px;
}

VisibilityToolWindow > PopupWindow {
    flex : 1 0;
}

VisibilityToolWindow > PopupWindow > #unity-content-container {
    flex : 1 0;
}

BoneVisibilityToolView {
    flex:1 0 auto;
}

SpriteVisibilityToolView {
    flex:1 0 auto;
}

MeshVisibilityToolView {
    flex:1 0 auto;
}

#VisibilityToolSelection {
    max-height : 20px;
    flex-direction : row;
    flex: 1 0 auto;
}

#VisibilityToolContainer {
    padding-bottom : 45px;
}

#VisibilityToolSelection > Label {
    flex:1 0 auto;
}

#HierarchyToolButtonImage {
    background-image: resource("Icons/HierarchyTool");
}

#VisibilityToolSelection > Button {
    margin: 0;
    padding: 0;
    border-left-width: 0;
    border-top-width: 0;
    border-right-width: 0;
    border-bottom-width: 0;
    cursor : link;
}

.visibilityToolTab {
    -unity-slice-left: 6;
    -unity-slice-top: 4;
    -unity-slice-right: 6;
    -unity-slice-bottom: 4;
    flex :1 0 auto;
    margin: 0;
    padding: 0;
    border-left-width: 0;
    border-top-width: 0;
    border-right-width: 0;
    border-bottom-width: 0;
}

.visibilityToolTab:hover:checked {
    background-color: var(--unity-colors-button-background-hover_pressed);
}

.visibilityToolTab:active {
    background-color: var(--unity-colors-button-background-pressed);
}

.visibilityToolTab:active:hover {
    background-color: var(--unity-colors-button-background-pressed);
}

#OpacitySliderGroup {
    margin-bottom : 5px;
    flex-direction:row;
    flex : 1 0 auto;
    max-height : 16px;
}

#BoneOpacitySliderGroup {
    flex-direction:row;
    flex-grow: 1;
    height : 16px;
}

#MeshOpacitySliderGroup {
    flex-direction:row;
    flex-grow: 1;
    height : 16px;
}

#MeshOpacitySlider {
    align-self : center;
    flex : 1 0;
}

#BoneOpacitySlider {
    align-self : center;
    flex : 1 0;
}

#BoneOpacitySliderIcon {
    background-image: url("../EditorIcons/Light/Bone.png");
    width : 16px;
    height : 16px;
}

#MeshOpacitySliderIcon {
    background-image: url("../EditorIcons/Light/Mesh.png");
    width : 16px;
    height : 16px;
}

.Dark #BoneOpacitySliderIcon {
    background-image: url("../EditorIcons/Dark/d_Bone.png");
    width : 16px;
    height : 16px;
}

.Dark  #MeshOpacitySliderIcon {
    background-image: url("../EditorIcons/Dark/d_Mesh.png");
    width : 16px;
    height : 16px;
}

BoneReparentToolView {
    flex : 1 0 auto;
}

#Resizer {
    cursor : resize-horizontal;
    min-width: 20px;
    margin-right: -15px;
}