# Pornstar Collection - Unity 2D Game

A Unity 2D game project with a professional main menu system featuring smooth animations, responsive design, and modern UI components.

## Project Overview

This project includes a complete main menu system with the following features:

### ✨ Features Implemented

- **Professional Main Menu Scene** with animated title and buttons
- **Smooth Animations** including fade-in effects, button hover animations, and scaling transitions
- **Responsive Design** that adapts to different screen resolutions and device types
- **Scene Management System** with smooth transitions between scenes
- **Visual Effects** including animated background gradients and particle systems
- **Modern UI Components** using Unity's UI system and TextMeshPro

### 🎮 Menu Components

- **Game Title**: "Pornstar Collection" with gradient text effect
- **Play Button**: Transitions to the game scene
- **Options Button**: Placeholder for options/settings scene
- **Quit Button**: Exits the application

### 🛠️ Technical Implementation

#### Scripts Created:

1. **MainMenuManager.cs** - Core menu functionality and animations
2. **MenuUIBuilder.cs** - Programmatic UI creation and layout
3. **ButtonHoverEffect.cs** - Enhanced button hover animations
4. **GameSceneManager.cs** - Scene transition management with fade effects
5. **ResponsiveUIManager.cs** - Responsive design for different screen sizes
6. **BackgroundManager.cs** - Animated background and visual effects

#### Scenes:

- **MainMenu.unity** - Main menu scene with all UI components
- **GameScene.unity** - Basic game scene for testing transitions
- **SampleScene.unity** - Original Unity sample scene

### 🎨 Visual Design

- **Color Scheme**: Modern dark theme with blue accents
- **Typography**: Bold, gradient text for the title
- **Animations**: Smooth fade-in, scaling, and hover effects
- **Background**: Animated gradient with optional particle effects
- **Responsive**: Adapts to mobile, tablet, and desktop screen sizes

### 🚀 Getting Started

1. **Open the Project**: Load the project in Unity 2022.3 or later
2. **Main Scene**: The main menu is located at `Assets/Scenes/MainMenu.unity`
3. **Play Mode**: Press Play in Unity to test the main menu
4. **Build Settings**: Scenes are already configured in Build Settings

### 📱 Responsive Features

The menu automatically adapts to different screen sizes:

- **Mobile** (≤768px): Smaller buttons, reduced spacing, scaled fonts
- **Tablet** (≤1024px): Medium-sized elements
- **Desktop** (>1024px): Full-sized elements

### 🎯 Customization Options

#### Menu Appearance:
- Modify colors in `MenuUIBuilder.cs`
- Adjust animation timings in `MainMenuManager.cs`
- Change background effects in `BackgroundManager.cs`

#### Button Configuration:
- Edit button texts in the MenuUIBuilder component
- Adjust hover effects in `ButtonHoverEffect.cs`
- Modify spacing and layout in ResponsiveUIManager

#### Scene Transitions:
- Configure scene names in `GameSceneManager.cs`
- Adjust fade duration and effects
- Add loading screens if needed

### 🔧 Components Breakdown

#### Canvas Setup:
- **Canvas Scaler**: Configured for 1920x1080 reference resolution
- **Graphic Raycaster**: Handles UI interactions
- **MainMenuManager**: Core menu logic
- **MenuUIBuilder**: Creates UI elements programmatically
- **ResponsiveUIManager**: Handles responsive design
- **BackgroundManager**: Manages visual effects

#### Animation System:
- Fade-in animations for menu entrance
- Staggered button animations
- Smooth hover effects with scaling
- Scene transition fades

### 📋 Next Steps

To extend this project, consider adding:

1. **Options Menu**: Settings for audio, graphics, controls
2. **Game Content**: Actual gameplay scenes and mechanics
3. **Save System**: Player progress and preferences
4. **Audio**: Background music and sound effects
5. **Localization**: Multi-language support

### 🎵 Audio Integration

The `ButtonHoverEffect.cs` script includes support for:
- Hover sound effects
- Click sound effects
- AudioSource component management

### 📐 Technical Specifications

- **Unity Version**: 2022.3 LTS or later
- **Render Pipeline**: Universal Render Pipeline (URP)
- **UI System**: Unity UI with TextMeshPro
- **Resolution**: Responsive design supporting all common resolutions
- **Platform**: Configured for PC, but adaptable to mobile platforms

### 🐛 Troubleshooting

If you encounter issues:

1. **Missing TextMeshPro**: Import TextMeshPro essentials when prompted
2. **Script Errors**: Ensure all scripts are in the correct folders
3. **Scene Loading**: Check that scenes are added to Build Settings
4. **UI Not Visible**: Verify Canvas and Camera settings

### 📝 Notes

- The project uses programmatic UI creation for flexibility
- All animations are coroutine-based for smooth performance
- Scene management includes proper cleanup and transitions
- Responsive design works across different aspect ratios

This main menu system provides a solid foundation for any Unity 2D game project with professional-quality UI and smooth user experience.
