using UnityEngine;
using UnityEngine.SceneManagement;
using System.Collections;

public class GameSceneManager : MonoBehaviour
{
    [Header("Scene Names")]
    public string mainMenuScene = "MainMenu";
    public string gameScene = "GameScene";
    public string optionsScene = "OptionsScene";
    public string creditsScene = "CreditsScene";
    
    [Header("Transition Settings")]
    public float transitionDuration = 1.0f;
    public bool useLoadingScreen = true;
    
    [Header("Loading Screen")]
    public GameObject loadingScreenPrefab;
    public string loadingText = "Loading...";
    
    private static GameSceneManager instance;
    private bool isTransitioning = false;
    
    public static GameSceneManager Instance
    {
        get
        {
            if (instance == null)
            {
                instance = FindObjectOfType<GameSceneManager>();
                if (instance == null)
                {
                    GameObject go = new GameObject("GameSceneManager");
                    instance = go.AddComponent<GameSceneManager>();
                    DontDestroyOnLoad(go);
                }
            }
            return instance;
        }
    }
    
    void Awake()
    {
        if (instance == null)
        {
            instance = this;
            DontDestroyOnLoad(gameObject);
        }
        else if (instance != this)
        {
            Destroy(gameObject);
        }
    }
    
    // Public methods for scene transitions
    public void LoadMainMenu()
    {
        LoadScene(mainMenuScene);
    }
    
    public void LoadGameScene()
    {
        LoadScene(gameScene);
    }
    
    public void LoadOptionsScene()
    {
        LoadScene(optionsScene);
    }
    
    public void LoadCreditsScene()
    {
        LoadScene(creditsScene);
    }
    
    public void ReloadCurrentScene()
    {
        string currentScene = SceneManager.GetActiveScene().name;
        LoadScene(currentScene);
    }
    
    public void QuitGame()
    {
        #if UNITY_EDITOR
            UnityEditor.EditorApplication.isPlaying = false;
        #else
            Application.Quit();
        #endif
    }
    
    // Core scene loading method
    public void LoadScene(string sceneName)
    {
        if (isTransitioning)
        {
            Debug.LogWarning("Scene transition already in progress!");
            return;
        }
        
        if (string.IsNullOrEmpty(sceneName))
        {
            Debug.LogError("Scene name is null or empty!");
            return;
        }
        
        StartCoroutine(LoadSceneAsync(sceneName));
    }
    
    IEnumerator LoadSceneAsync(string sceneName)
    {
        isTransitioning = true;
        
        // Fade out current scene
        yield return StartCoroutine(FadeOut());
        
        // Show loading screen if enabled
        GameObject loadingScreen = null;
        if (useLoadingScreen && loadingScreenPrefab != null)
        {
            loadingScreen = Instantiate(loadingScreenPrefab);
            DontDestroyOnLoad(loadingScreen);
        }
        
        // Start loading the scene asynchronously
        AsyncOperation asyncLoad = SceneManager.LoadSceneAsync(sceneName);
        asyncLoad.allowSceneActivation = false;
        
        // Wait for scene to load
        while (asyncLoad.progress < 0.9f)
        {
            Debug.Log($"Loading progress: {asyncLoad.progress * 100}%");
            yield return null;
        }
        
        // Wait a minimum time for loading screen
        yield return new WaitForSeconds(0.5f);
        
        // Activate the scene
        asyncLoad.allowSceneActivation = true;
        
        // Wait for scene to be fully loaded
        yield return new WaitUntil(() => asyncLoad.isDone);
        
        // Remove loading screen
        if (loadingScreen != null)
        {
            Destroy(loadingScreen);
        }
        
        // Fade in new scene
        yield return StartCoroutine(FadeIn());
        
        isTransitioning = false;
        
        Debug.Log($"Successfully loaded scene: {sceneName}");
    }
    
    IEnumerator FadeOut()
    {
        // Find any canvas groups in the scene for fading
        CanvasGroup[] canvasGroups = FindObjectsOfType<CanvasGroup>();
        
        float elapsedTime = 0f;
        float[] originalAlphas = new float[canvasGroups.Length];
        
        // Store original alpha values
        for (int i = 0; i < canvasGroups.Length; i++)
        {
            originalAlphas[i] = canvasGroups[i].alpha;
        }
        
        while (elapsedTime < transitionDuration)
        {
            elapsedTime += Time.deltaTime;
            float alpha = Mathf.Lerp(1f, 0f, elapsedTime / transitionDuration);
            
            for (int i = 0; i < canvasGroups.Length; i++)
            {
                canvasGroups[i].alpha = Mathf.Lerp(originalAlphas[i], 0f, elapsedTime / transitionDuration);
            }
            
            yield return null;
        }
        
        // Ensure final alpha is 0
        foreach (CanvasGroup cg in canvasGroups)
        {
            cg.alpha = 0f;
        }
    }
    
    IEnumerator FadeIn()
    {
        yield return new WaitForSeconds(0.1f); // Small delay to ensure scene is ready
        
        CanvasGroup[] canvasGroups = FindObjectsOfType<CanvasGroup>();
        
        float elapsedTime = 0f;
        
        while (elapsedTime < transitionDuration)
        {
            elapsedTime += Time.deltaTime;
            float alpha = Mathf.Lerp(0f, 1f, elapsedTime / transitionDuration);
            
            foreach (CanvasGroup cg in canvasGroups)
            {
                cg.alpha = alpha;
            }
            
            yield return null;
        }
        
        // Ensure final alpha is 1
        foreach (CanvasGroup cg in canvasGroups)
        {
            cg.alpha = 1f;
        }
    }
    
    // Utility methods
    public bool IsSceneLoaded(string sceneName)
    {
        return SceneManager.GetActiveScene().name == sceneName;
    }
    
    public string GetCurrentSceneName()
    {
        return SceneManager.GetActiveScene().name;
    }
    
    public bool IsTransitioning()
    {
        return isTransitioning;
    }
}
