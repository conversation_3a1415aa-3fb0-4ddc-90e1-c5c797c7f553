{"displayName": "Procedural Patterns Subgraph Examples", "interactiveImport": "false", "description": "This collection of subgraphs showcases various procedural techniques possible with shadergraph. Use them in your project or edit them to create other procedural pattens. Subgraph patterns: Bacter<PERSON>, Brick, <PERSON>s, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>oth Wave, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>hirl, Zig Zag", "createSeparatePackage": true}