{
    "m_SGVersion": 3,
    "m_Type": "UnityEditor.ShaderGraph.GraphData",
    "m_ObjectId": "a805d50baeec43e688c68613cba85f5b",
    "m_Properties": [
        {
            "m_Id": "2a63d0b0194144d6b8cb44184581a7bf"
        }
    ],
    "m_Keywords": [],
    "m_Dropdowns": [],
    "m_CategoryData": [
        {
            "m_Id": "e6ef3e8653e54ab4b528b830c517ec15"
        }
    ],
    "m_Nodes": [
        {
            "m_Id": "d47abf6eb8234e1c8deb90ae9dae0c62"
        },
        {
            "m_Id": "5679c699a8824f3f842ca904210e7529"
        },
        {
            "m_Id": "20799078114c4b6db04ea1100b7eef61"
        }
    ],
    "m_GroupDatas": [],
    "m_StickyNoteDatas": [
        {
            "m_Id": "905aaf5cdc4a4e16a258133cbf3c494a"
        }
    ],
    "m_Edges": [
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "20799078114c4b6db04ea1100b7eef61"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "d47abf6eb8234e1c8deb90ae9dae0c62"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "5679c699a8824f3f842ca904210e7529"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "20799078114c4b6db04ea1100b7eef61"
                },
                "m_SlotId": 1
            }
        }
    ],
    "m_VertexContext": {
        "m_Position": {
            "x": 0.0,
            "y": 0.0
        },
        "m_Blocks": []
    },
    "m_FragmentContext": {
        "m_Position": {
            "x": 0.0,
            "y": 0.0
        },
        "m_Blocks": []
    },
    "m_PreviewData": {
        "serializedMesh": {
            "m_SerializedMesh": "{\"mesh\":{\"instanceID\":0}}",
            "m_Guid": ""
        },
        "preventRotation": false
    },
    "m_Path": "Procedural/Noise",
    "m_GraphPrecision": 1,
    "m_PreviewMode": 2,
    "m_OutputNode": {
        "m_Id": "d47abf6eb8234e1c8deb90ae9dae0c62"
    },
    "m_SubDatas": [],
    "m_ActiveTargets": []
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.CustomFunctionNode",
    "m_ObjectId": "20799078114c4b6db04ea1100b7eef61",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Tchou33 (Custom Function)",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1830.0,
            "y": -214.00001525878907,
            "width": 206.0,
            "height": 94.00001525878906
        }
    },
    "m_Slots": [
        {
            "m_Id": "d8a1e067c1194567ae4c7188c03d3e68"
        },
        {
            "m_Id": "4917cf32298946ed8ad49d83dbba2827"
        }
    ],
    "synonyms": [
        "code",
        "HLSL"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SourceType": 1,
    "m_FunctionName": "Tchou33",
    "m_FunctionSource": "",
    "m_FunctionBody": "//tchou33\r\nuint3 v = (uint3) (int3) round(p);\r\n    v.x ^= 1103515245U;\n    v.y ^= v.x + v.z;\n    v.y = v.y * 134775813;\n    v.z += v.x ^ v.y;\n    v.y += v.x ^ v.z;\n    v.x += v.y * v.z;\n    v.x = v.x * 0x27d4eb2du;\n    v.z ^= v.x << 3;\n    v.y += v.z << 3; \r\r\nOut = v * (1.0 / float(0xffffffff));"
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.Internal.Vector3ShaderProperty",
    "m_ObjectId": "2a63d0b0194144d6b8cb44184581a7bf",
    "m_Guid": {
        "m_GuidSerialized": "ca88e2c4-4926-4b5b-9ac9-8be19fbc9474"
    },
    "m_Name": "In",
    "m_DefaultRefNameVersion": 1,
    "m_RefNameGeneratedByDisplayName": "In",
    "m_DefaultReferenceName": "_In",
    "m_OverrideReferenceName": "",
    "m_GeneratePropertyBlock": true,
    "m_UseCustomSlotLabel": false,
    "m_CustomSlotLabel": "",
    "m_DismissedVersion": 0,
    "m_Precision": 0,
    "overrideHLSLDeclaration": false,
    "hlslDeclarationOverride": 0,
    "m_Hidden": false,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "4917cf32298946ed8ad49d83dbba2827",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PropertyNode",
    "m_ObjectId": "5679c699a8824f3f842ca904210e7529",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Property",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1916.0001220703125,
            "y": -174.6666717529297,
            "width": 86.6666259765625,
            "height": 33.99998474121094
        }
    },
    "m_Slots": [
        {
            "m_Id": "e65d414797654ad2a161727564dcb821"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Property": {
        "m_Id": "2a63d0b0194144d6b8cb44184581a7bf"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "905aaf5cdc4a4e16a258133cbf3c494a",
    "m_Title": "",
    "m_Content": "Generates a random output value for every unique input value.\n\nThis one receives a Vec3 and outputs a Vec3.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1827.0001220703125,
        "y": -317.5000305175781,
        "width": 200.0,
        "height": 100.00001525878906
    },
    "m_Group": {
        "m_Id": ""
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "c5aca2b3b1dc453f815710fd4c3d676a",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SubGraphOutputNode",
    "m_ObjectId": "d47abf6eb8234e1c8deb90ae9dae0c62",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Output",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1624.0,
            "y": -214.00001525878907,
            "width": 85.333251953125,
            "height": 76.66667175292969
        }
    },
    "m_Slots": [
        {
            "m_Id": "c5aca2b3b1dc453f815710fd4c3d676a"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "IsFirstSlotValid": true
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "d8a1e067c1194567ae4c7188c03d3e68",
    "m_Id": 1,
    "m_DisplayName": "p",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "p",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "e65d414797654ad2a161727564dcb821",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.CategoryData",
    "m_ObjectId": "e6ef3e8653e54ab4b528b830c517ec15",
    "m_Name": "",
    "m_ChildObjectList": [
        {
            "m_Id": "2a63d0b0194144d6b8cb44184581a7bf"
        }
    ]
}

