%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &178795931696169881
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1049764582655622063, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
  m_PrefabInstance: {fileID: 3325690817238656555}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 555687331671359702}
  - component: {fileID: 4718570395609496763}
  - component: {fileID: 7201009384160301370}
  m_Layer: 0
  m_Name: FrameCorner (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: **********
  m_IsActive: 1
--- !u!4 &555687331671359702
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 8269593330339784379, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
  m_PrefabInstance: {fileID: 3325690817238656555}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 178795931696169881}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0.70710677, z: -0, w: 0.7071068}
  m_LocalPosition: {x: 3, y: 0, z: 3}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7716844785207754693}
  m_LocalEulerAnglesHint: {x: 0, y: -90, z: 0}
--- !u!33 &4718570395609496763
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1521097648360361569, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
  m_PrefabInstance: {fileID: 3325690817238656555}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 178795931696169881}
  m_Mesh: {fileID: 4969049306130400984, guid: 9625ac777d70ea648a19cdbbe169b438, type: 3}
--- !u!23 &7201009384160301370
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1377013479803641083, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
  m_PrefabInstance: {fileID: 3325690817238656555}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 178795931696169881}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 257
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 735e5139c2106f54096a4ebbeea42ac7, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &338104040999595249
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9179295491562172987}
  - component: {fileID: 1993450540141088532}
  - component: {fileID: 4514897403199153920}
  - component: {fileID: 2423373501444699475}
  m_Layer: 0
  m_Name: Cube (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: **********
  m_IsActive: 1
--- !u!4 &9179295491562172987
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 338104040999595249}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4303619488855159463}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 90}
--- !u!33 &1993450540141088532
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 338104040999595249}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &4514897403199153920
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 338104040999595249}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &2423373501444699475
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 338104040999595249}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1.0000005, y: 1, z: 1.0000005}
  m_Center: {x: 0, y: 0, z: 0.000007629398}
--- !u!1 &557151038971784052
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1995278512336704064, guid: 6c40ad478ff0e5a4c91d0b78f29e80e2, type: 3}
  m_PrefabInstance: {fileID: 1899071910888612704}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3650473551455121344}
  - component: {fileID: 8950994523530046048}
  - component: {fileID: 4294322791541475727}
  m_Layer: 0
  m_Name: Info
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: **********
  m_IsActive: 1
--- !u!224 &3650473551455121344
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 425122075988965178, guid: 6c40ad478ff0e5a4c91d0b78f29e80e2, type: 3}
  m_PrefabInstance: {fileID: 1899071910888612704}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 557151038971784052}
  m_LocalRotation: {x: -0, y: 1, z: -0, w: 0}
  m_LocalPosition: {x: 0, y: 0, z: 0.212}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8013054990551701556}
  m_LocalEulerAnglesHint: {x: 0, y: 180, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: -1.017, y: 1.2439}
  m_SizeDelta: {x: 1.5, y: 1.9193}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!23 &8950994523530046048
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 2296844143558358538, guid: 6c40ad478ff0e5a4c91d0b78f29e80e2, type: 3}
  m_PrefabInstance: {fileID: 1899071910888612704}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 557151038971784052}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 257
  m_RendererPriority: 0
  m_Materials:
  - {fileID: -6055507529455485361, guid: 2f7116f10747a67409388e93052ae222, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!114 &4294322791541475727
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 2759006676346111776, guid: 6c40ad478ff0e5a4c91d0b78f29e80e2, type: 3}
  m_PrefabInstance: {fileID: 1899071910888612704}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 557151038971784052}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9541d86e2fd84c1d9990edf0852d74ab, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: "Decals allow you to apply local material modifications to specific places
    in the world. You might think of things like applying graffiti tags to a wall
    or scattering fallen leaves below a tree. But decals can be used for a lot more.
    In these examples, we see decals making things look wet, making surfaces appear
    to have flowing water across them, projecting water caustics, and blending specific
    materials onto other objects.\r\n\r\nDecals are available to use in both HDRP
    and URP, but they need to be enabled in both render pipelines. To use decals,
    see the documentation in both HDRP and URP.\r"
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 2f7116f10747a67409388e93052ae222, type: 2}
  m_sharedMaterial: {fileID: -6055507529455485361, guid: 2f7116f10747a67409388e93052ae222, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 0.6
  m_fontSizeBase: 0.6
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 1
  m_VerticalAlignment: 256
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 1
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 1
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 0
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: -0.00075912476, y: -0.005089283, z: 0.019396782, w: -0.001572609}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  _SortingLayer: 0
  _SortingLayerID: 0
  _SortingOrder: 0
  m_hasFontAssetChanged: 0
  m_renderer: {fileID: 8950994523530046048}
  m_maskType: 0
--- !u!1 &1123598307265033043
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4871824531432446743}
  - component: {fileID: 6747498740584875798}
  - component: {fileID: 8097453625556535828}
  - component: {fileID: 6515853786770802579}
  m_Layer: 0
  m_Name: Cube (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: **********
  m_IsActive: 1
--- !u!4 &4871824531432446743
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1123598307265033043}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6729831840931939383}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 90}
--- !u!33 &6747498740584875798
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1123598307265033043}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &8097453625556535828
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1123598307265033043}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &6515853786770802579
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1123598307265033043}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1.0000005, y: 1, z: 1.0000005}
  m_Center: {x: 0, y: 0, z: 0.000007629398}
--- !u!1 &1646525878820722683
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3686561093468324311}
  - component: {fileID: 1829136806410145251}
  - component: {fileID: 1019273486439935487}
  - component: {fileID: 7088698237515305095}
  m_Layer: 0
  m_Name: Cube (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: **********
  m_IsActive: 1
--- !u!4 &3686561093468324311
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1646525878820722683}
  serializedVersion: 2
  m_LocalRotation: {x: 0.5, y: -0.5, z: -0.5, w: 0.5}
  m_LocalPosition: {x: 0, y: 5, z: -0.49999982}
  m_LocalScale: {x: 10, y: 0.10000003, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7075162569821107308}
  m_LocalEulerAnglesHint: {x: 90, y: 0, z: 0}
--- !u!33 &1829136806410145251
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1646525878820722683}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &1019273486439935487
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1646525878820722683}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &7088698237515305095
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1646525878820722683}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1.0000005, y: 1, z: 1.0000005}
  m_Center: {x: 0, y: 0, z: 0.000007629398}
--- !u!1 &1712736119894525174
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 50457568970307013, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
  m_PrefabInstance: {fileID: 3325690817238656555}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7716844785207754693}
  m_Layer: 0
  m_Name: Platform
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: **********
  m_IsActive: 1
--- !u!4 &7716844785207754693
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 6915846320344479465, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
  m_PrefabInstance: {fileID: 3325690817238656555}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1712736119894525174}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0.7071068, z: 0, w: 0.7071068}
  m_LocalPosition: {x: -1, y: -0.5, z: 3}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6349198775541576308}
  - {fileID: 1345598341540960552}
  - {fileID: 555687331671359702}
  - {fileID: 6774119350379864979}
  - {fileID: 6696782431803533109}
  - {fileID: 856256668810449611}
  - {fileID: 922756289092827361}
  - {fileID: 2164662150448546844}
  - {fileID: 8688129509291715998}
  - {fileID: 3771897528352836944}
  m_Father: {fileID: 8981432136717117525}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1969292993271932228
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 3753311242483336413, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
  m_PrefabInstance: {fileID: 3325690817238656555}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2164662150448546844}
  - component: {fileID: 8176822360662862451}
  - component: {fileID: 821601330097686426}
  m_Layer: 0
  m_Name: FrameSide (3)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: **********
  m_IsActive: 1
--- !u!4 &2164662150448546844
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 2560219806050898391, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
  m_PrefabInstance: {fileID: 3325690817238656555}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1969292993271932228}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: 0.70710677, z: -0, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: -3}
  m_LocalScale: {x: 1, y: 1, z: 2}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7716844785207754693}
  m_LocalEulerAnglesHint: {x: 0, y: 90, z: 0}
--- !u!33 &8176822360662862451
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 6371524492065344932, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
  m_PrefabInstance: {fileID: 3325690817238656555}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1969292993271932228}
  m_Mesh: {fileID: 2970638267290972382, guid: 1bf2e27b5c9b0d945b8fdbbf1165f828, type: 3}
--- !u!23 &821601330097686426
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 8545129148217422385, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
  m_PrefabInstance: {fileID: 3325690817238656555}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1969292993271932228}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 257
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 735e5139c2106f54096a4ebbeea42ac7, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &2011616065094866682
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 7726019937898060624, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
  m_PrefabInstance: {fileID: 3325690817238656555}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1345598341540960552}
  - component: {fileID: 4310708149640848063}
  - component: {fileID: 7194226047250214194}
  m_Layer: 0
  m_Name: FrameSide
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: **********
  m_IsActive: 1
--- !u!4 &1345598341540960552
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4995095618031977957, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
  m_PrefabInstance: {fileID: 3325690817238656555}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2011616065094866682}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 3, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 2}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7716844785207754693}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &4310708149640848063
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 7411640925357202048, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
  m_PrefabInstance: {fileID: 3325690817238656555}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2011616065094866682}
  m_Mesh: {fileID: 2970638267290972382, guid: 1bf2e27b5c9b0d945b8fdbbf1165f828, type: 3}
--- !u!23 &7194226047250214194
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 6462473818970603407, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
  m_PrefabInstance: {fileID: 3325690817238656555}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2011616065094866682}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 257
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 735e5139c2106f54096a4ebbeea42ac7, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &2497141074540176397
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4135125235891897624, guid: 6c40ad478ff0e5a4c91d0b78f29e80e2, type: 3}
  m_PrefabInstance: {fileID: 1899071910888612704}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6205873398370969156}
  - component: {fileID: 6435121812069700441}
  - component: {fileID: 6159723627956203628}
  m_Layer: 0
  m_Name: Panel
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: **********
  m_IsActive: 1
--- !u!4 &6205873398370969156
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 7662022838613109087, guid: 6c40ad478ff0e5a4c91d0b78f29e80e2, type: 3}
  m_PrefabInstance: {fileID: 1899071910888612704}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2497141074540176397}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8013054990551701556}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &6435121812069700441
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 7448974264483159729, guid: 6c40ad478ff0e5a4c91d0b78f29e80e2, type: 3}
  m_PrefabInstance: {fileID: 1899071910888612704}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2497141074540176397}
  m_Mesh: {fileID: 6464809856111060268, guid: b89a6265faae8034d8dfb2e812ab689e, type: 3}
--- !u!23 &6159723627956203628
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 7989275264558943083, guid: 6c40ad478ff0e5a4c91d0b78f29e80e2, type: 3}
  m_PrefabInstance: {fileID: 1899071910888612704}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2497141074540176397}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 257
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 735e5139c2106f54096a4ebbeea42ac7, type: 2}
  - {fileID: 2100000, guid: 55610f65dcca6294b9fc9386d9879d82, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &2925458857229308161
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7075162569821107308}
  m_Layer: 0
  m_Name: GameObject
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: **********
  m_IsActive: 1
--- !u!4 &7075162569821107308
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2925458857229308161}
  serializedVersion: 2
  m_LocalRotation: {x: 0.27059805, y: 0.27059805, z: 0.6532815, w: 0.6532815}
  m_LocalPosition: {x: 0.36425102, y: 0.699999, z: -0.35838655}
  m_LocalScale: {x: 1, y: 0.10000002, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4771084367819536138}
  - {fileID: 3686561093468324311}
  - {fileID: 3273409636818177082}
  m_Father: {fileID: 6268656707265246860}
  m_LocalEulerAnglesHint: {x: 0, y: 45, z: 90}
--- !u!1 &2980378483777634142
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2586242422041734719}
  - component: {fileID: 1022940796086029100}
  - component: {fileID: 8826505378568316038}
  - component: {fileID: 2213466879762405239}
  m_Layer: 0
  m_Name: Cube
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: **********
  m_IsActive: 1
--- !u!4 &2586242422041734719
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2980378483777634142}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -0.50000006, y: 5, z: 0}
  m_LocalScale: {x: 10, y: 0.09999998, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4303619488855159463}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &1022940796086029100
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2980378483777634142}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &8826505378568316038
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2980378483777634142}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &2213466879762405239
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2980378483777634142}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1.0000005, y: 1, z: 1.0000005}
  m_Center: {x: 0, y: 0, z: 0.000007629398}
--- !u!1 &3842005569555301214
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4307562375208550975}
  - component: {fileID: 6850504369253378466}
  m_Layer: 0
  m_Name: Decal Projector
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: **********
  m_IsActive: 1
--- !u!4 &4307562375208550975
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3842005569555301214}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0, y: 0.643, z: -0.173}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 537608235367061567}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &6850504369253378466
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3842005569555301214}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0777d029ed3dffa4692f417d4aba19ca, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 2100000, guid: 4a10010bfd725754f8425e43556fd915, type: 2}
  m_DrawDistance: 1000
  m_FadeScale: 0.9
  m_StartAngleFade: 180
  m_EndAngleFade: 180
  m_UVScale: {x: 1, y: 1}
  m_UVBias: {x: 0, y: 0}
  m_DecalLayerMask: 1
  m_ScaleMode: 0
  m_Offset: {x: 0, y: 0, z: 0}
  m_Size: {x: 2, y: 2, z: 2}
  m_FadeFactor: 1
--- !u!1 &4070997659496678418
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 3250075270326530992, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
  m_PrefabInstance: {fileID: 3325690817238656555}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6696782431803533109}
  - component: {fileID: 4733371931226030849}
  - component: {fileID: 4919128249466521318}
  m_Layer: 0
  m_Name: FrameSide (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: **********
  m_IsActive: 1
--- !u!4 &6696782431803533109
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 3776254613730470933, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
  m_PrefabInstance: {fileID: 3325690817238656555}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4070997659496678418}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -1, z: -0, w: -0.00000037252897}
  m_LocalPosition: {x: -3, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 2}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7716844785207754693}
  m_LocalEulerAnglesHint: {x: 0, y: -180, z: 0}
--- !u!33 &4733371931226030849
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 6541066194258249760, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
  m_PrefabInstance: {fileID: 3325690817238656555}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4070997659496678418}
  m_Mesh: {fileID: 2970638267290972382, guid: 1bf2e27b5c9b0d945b8fdbbf1165f828, type: 3}
--- !u!23 &4919128249466521318
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 5193325140313856829, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
  m_PrefabInstance: {fileID: 3325690817238656555}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4070997659496678418}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 257
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 735e5139c2106f54096a4ebbeea42ac7, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &4111158002096184621
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1537572312732349544, guid: 6c40ad478ff0e5a4c91d0b78f29e80e2, type: 3}
  m_PrefabInstance: {fileID: 1899071910888612704}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 374193507074498409}
  - component: {fileID: 5591059317392546233}
  - component: {fileID: 2149762589610769643}
  m_Layer: 0
  m_Name: Title
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: **********
  m_IsActive: 1
--- !u!224 &374193507074498409
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 8052883244233579186, guid: 6c40ad478ff0e5a4c91d0b78f29e80e2, type: 3}
  m_PrefabInstance: {fileID: 1899071910888612704}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4111158002096184621}
  m_LocalRotation: {x: -0, y: 1, z: -0, w: 0}
  m_LocalPosition: {x: 0, y: 0, z: 0.208}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8013054990551701556}
  m_LocalEulerAnglesHint: {x: 0, y: 180, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: -1.0322, y: 2.625}
  m_SizeDelta: {x: 1.6984, y: 0.2}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!23 &5591059317392546233
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 6808522092965265429, guid: 6c40ad478ff0e5a4c91d0b78f29e80e2, type: 3}
  m_PrefabInstance: {fileID: 1899071910888612704}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4111158002096184621}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 257
  m_RendererPriority: 0
  m_Materials:
  - {fileID: -6055507529455485361, guid: 2f7116f10747a67409388e93052ae222, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!114 &2149762589610769643
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1137507590067690614, guid: 6c40ad478ff0e5a4c91d0b78f29e80e2, type: 3}
  m_PrefabInstance: {fileID: 1899071910888612704}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4111158002096184621}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9541d86e2fd84c1d9990edf0852d74ab, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: Decals
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 2f7116f10747a67409388e93052ae222, type: 2}
  m_sharedMaterial: {fileID: -6055507529455485361, guid: 2f7116f10747a67409388e93052ae222, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 2
  m_fontSizeBase: 2
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 1
  m_VerticalAlignment: 256
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 1
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 0
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0.002163887, y: 0.001461029, z: -0.0054740906, w: -0.003365755}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  _SortingLayer: 0
  _SortingLayerID: 0
  _SortingOrder: 0
  m_hasFontAssetChanged: 0
  m_renderer: {fileID: 5591059317392546233}
  m_maskType: 0
--- !u!1 &4436668306989869605
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 6270412221303966579, guid: 6c40ad478ff0e5a4c91d0b78f29e80e2, type: 3}
  m_PrefabInstance: {fileID: 1899071910888612704}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8013054990551701556}
  m_Layer: 0
  m_Name: InfoPanel
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: **********
  m_IsActive: 1
--- !u!4 &8013054990551701556
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 823572905295939120, guid: 6c40ad478ff0e5a4c91d0b78f29e80e2, type: 3}
  m_PrefabInstance: {fileID: 1899071910888612704}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4436668306989869605}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 374193507074498409}
  - {fileID: 3650473551455121344}
  - {fileID: 6205873398370969156}
  m_Father: {fileID: 8981432136717117525}
  m_LocalEulerAnglesHint: {x: 0, y: 90, z: 0}
--- !u!1 &4464521237210123222
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1512132155333820600, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
  m_PrefabInstance: {fileID: 3325690817238656555}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8688129509291715998}
  - component: {fileID: 6906605280249579198}
  - component: {fileID: 3934622954944010987}
  - component: {fileID: 3765343688215820514}
  m_Layer: 0
  m_Name: platform
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: **********
  m_IsActive: 1
--- !u!4 &8688129509291715998
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 587019661340190893, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
  m_PrefabInstance: {fileID: 3325690817238656555}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4464521237210123222}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0.25, z: 0}
  m_LocalScale: {x: 8, y: 0.5, z: 8}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7716844785207754693}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &6906605280249579198
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1107127272255842825, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
  m_PrefabInstance: {fileID: 3325690817238656555}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4464521237210123222}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &3934622954944010987
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 7227594095328192960, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
  m_PrefabInstance: {fileID: 3325690817238656555}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4464521237210123222}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 257
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 25a9739d74f47314c9cd2d850a5357c0, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &3765343688215820514
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 7358068292185304304, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
  m_PrefabInstance: {fileID: 3325690817238656555}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4464521237210123222}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!1 &4499836248687632581
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 685220620642898123}
  - component: {fileID: 1000184247196447479}
  - component: {fileID: 1632838159801957553}
  - component: {fileID: 4406445472626448854}
  m_Layer: 0
  m_Name: Cube (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: **********
  m_IsActive: 1
--- !u!4 &685220620642898123
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4499836248687632581}
  serializedVersion: 2
  m_LocalRotation: {x: 0.5, y: -0.5, z: -0.5, w: 0.5}
  m_LocalPosition: {x: 0, y: 5, z: -0.49999982}
  m_LocalScale: {x: 10, y: 0.10000003, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 846161440402699366}
  m_LocalEulerAnglesHint: {x: 90, y: 0, z: 0}
--- !u!33 &1000184247196447479
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4499836248687632581}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &1632838159801957553
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4499836248687632581}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &4406445472626448854
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4499836248687632581}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1.0000005, y: 1, z: 1.0000005}
  m_Center: {x: 0, y: 0, z: 0.000007629398}
--- !u!1 &4683065801925061650
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7214431815756526463}
  - component: {fileID: 7934266596912642499}
  - component: {fileID: 4344031148900134893}
  - component: {fileID: 8976953560839275075}
  m_Layer: 0
  m_Name: Cube (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: **********
  m_IsActive: 1
--- !u!4 &7214431815756526463
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4683065801925061650}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 846161440402699366}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 90}
--- !u!33 &7934266596912642499
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4683065801925061650}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &4344031148900134893
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4683065801925061650}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &8976953560839275075
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4683065801925061650}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1.0000005, y: 1, z: 1.0000005}
  m_Center: {x: 0, y: 0, z: 0.000007629398}
--- !u!1 &4868441303434235647
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 93298385648041818, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
  m_PrefabInstance: {fileID: 3325690817238656555}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6774119350379864979}
  - component: {fileID: 5459404620511355490}
  - component: {fileID: 644424168480604075}
  m_Layer: 0
  m_Name: FrameCorner (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: **********
  m_IsActive: 1
--- !u!4 &6774119350379864979
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 8065442033774228784, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
  m_PrefabInstance: {fileID: 3325690817238656555}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4868441303434235647}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0.7071063, z: -0, w: -0.70710737}
  m_LocalPosition: {x: -3, y: 0, z: -3}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7716844785207754693}
  m_LocalEulerAnglesHint: {x: 0, y: -270, z: 0}
--- !u!33 &5459404620511355490
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 2328485129702806638, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
  m_PrefabInstance: {fileID: 3325690817238656555}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4868441303434235647}
  m_Mesh: {fileID: 4969049306130400984, guid: 9625ac777d70ea648a19cdbbe169b438, type: 3}
--- !u!23 &644424168480604075
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 6154641838871061945, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
  m_PrefabInstance: {fileID: 3325690817238656555}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4868441303434235647}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 257
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 735e5139c2106f54096a4ebbeea42ac7, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &5014087129523855321
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4558290094239230542}
  - component: {fileID: 8349168345101153194}
  m_Layer: 0
  m_Name: Decal Projector
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: **********
  m_IsActive: 1
--- !u!4 &4558290094239230542
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5014087129523855321}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0, y: 0.643, z: -0.173}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7745483497612582546}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &8349168345101153194
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5014087129523855321}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0777d029ed3dffa4692f417d4aba19ca, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: -876546973899608171, guid: ccb856833d228344ca1a5694af1771b2, type: 3}
  m_DrawDistance: 1000
  m_FadeScale: 0.9
  m_StartAngleFade: 180
  m_EndAngleFade: 180
  m_UVScale: {x: 1, y: 1}
  m_UVBias: {x: 0, y: 0}
  m_DecalLayerMask: 1
  m_ScaleMode: 0
  m_Offset: {x: 0, y: 0, z: 0}
  m_Size: {x: 1.2, y: 1.2, z: 1.2}
  m_FadeFactor: 1
--- !u!1 &5295242291052066987
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 7817475782026850255, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
  m_PrefabInstance: {fileID: 3325690817238656555}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 856256668810449611}
  - component: {fileID: 9187687042692471316}
  - component: {fileID: 5483789122635301285}
  m_Layer: 0
  m_Name: FrameCorner (3)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: **********
  m_IsActive: 1
--- !u!4 &856256668810449611
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 3246467193877922269, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
  m_PrefabInstance: {fileID: 3325690817238656555}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5295242291052066987}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -1, z: -0, w: -0.0000009238719}
  m_LocalPosition: {x: -3, y: 0, z: 3}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7716844785207754693}
  m_LocalEulerAnglesHint: {x: 0, y: -180, z: 0}
--- !u!33 &9187687042692471316
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 5250597764988438844, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
  m_PrefabInstance: {fileID: 3325690817238656555}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5295242291052066987}
  m_Mesh: {fileID: 4969049306130400984, guid: 9625ac777d70ea648a19cdbbe169b438, type: 3}
--- !u!23 &5483789122635301285
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 791041433055833261, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
  m_PrefabInstance: {fileID: 3325690817238656555}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5295242291052066987}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 257
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 735e5139c2106f54096a4ebbeea42ac7, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &5954068885960636734
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7431395919633597144}
  - component: {fileID: 7901556616506607907}
  - component: {fileID: 626117185776907023}
  - component: {fileID: 1095808092240633331}
  m_Layer: 0
  m_Name: Cube
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: **********
  m_IsActive: 1
--- !u!4 &7431395919633597144
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5954068885960636734}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -0.50000006, y: 5, z: 0}
  m_LocalScale: {x: 10, y: 0.09999998, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6729831840931939383}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &7901556616506607907
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5954068885960636734}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &626117185776907023
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5954068885960636734}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &1095808092240633331
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5954068885960636734}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1.0000005, y: 1, z: 1.0000005}
  m_Center: {x: 0, y: 0, z: 0.000007629398}
--- !u!1 &5998735092625368499
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7358328783974989560}
  - component: {fileID: 5800804623687702988}
  m_Layer: 0
  m_Name: Decal Projector
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: **********
  m_IsActive: 1
--- !u!4 &7358328783974989560
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5998735092625368499}
  serializedVersion: 2
  m_LocalRotation: {x: -0.27059805, y: -0.27059805, z: -0.6532815, w: 0.6532815}
  m_LocalPosition: {x: -0.0000014156105, y: 0.64300525, z: -0.17292115}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6268656707265246860}
  m_LocalEulerAnglesHint: {x: -45, y: 0, z: -90}
--- !u!114 &5800804623687702988
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5998735092625368499}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0777d029ed3dffa4692f417d4aba19ca, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 2100000, guid: c7c66dc12c5bb3f40a8ea658e83a90a8, type: 2}
  m_DrawDistance: 1000
  m_FadeScale: 0.9
  m_StartAngleFade: 180
  m_EndAngleFade: 180
  m_UVScale: {x: 1, y: 1}
  m_UVBias: {x: 0, y: 0}
  m_DecalLayerMask: 1
  m_ScaleMode: 0
  m_Offset: {x: 0, y: 0, z: 0}
  m_Size: {x: 2, y: 2, z: 2}
  m_FadeFactor: 1
--- !u!1 &6018677946767729227
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6729831840931939383}
  m_Layer: 0
  m_Name: GameObject
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: **********
  m_IsActive: 1
--- !u!4 &6729831840931939383
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6018677946767729227}
  serializedVersion: 2
  m_LocalRotation: {x: 0.27059805, y: 0.27059805, z: 0.6532815, w: 0.6532815}
  m_LocalPosition: {x: 0.36425102, y: 0.699999, z: -0.35838655}
  m_LocalScale: {x: 1, y: 0.10000002, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7431395919633597144}
  - {fileID: 9080273441979808096}
  - {fileID: 4871824531432446743}
  m_Father: {fileID: 7222192614336574123}
  m_LocalEulerAnglesHint: {x: 0, y: 45, z: 90}
--- !u!1 &6750566803412279031
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4303619488855159463}
  m_Layer: 0
  m_Name: GameObject
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: **********
  m_IsActive: 1
--- !u!4 &4303619488855159463
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6750566803412279031}
  serializedVersion: 2
  m_LocalRotation: {x: 0.27059805, y: 0.27059805, z: 0.6532815, w: 0.6532815}
  m_LocalPosition: {x: 0.36425102, y: 0.699999, z: -0.35838655}
  m_LocalScale: {x: 1, y: 0.10000002, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2586242422041734719}
  - {fileID: 8805501651928800704}
  - {fileID: 9179295491562172987}
  m_Father: {fileID: 537608235367061567}
  m_LocalEulerAnglesHint: {x: 0, y: 45, z: 90}
--- !u!1 &6913723326018898823
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8323059998458372410}
  - component: {fileID: 5779331733263534693}
  - component: {fileID: 2323405668482745897}
  - component: {fileID: 3544809171099342473}
  m_Layer: 0
  m_Name: Capsule
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8323059998458372410
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6913723326018898823}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0.024, y: 0.009, z: -0.244}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6268656707265246860}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &5779331733263534693
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6913723326018898823}
  m_Mesh: {fileID: 10208, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &2323405668482745897
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6913723326018898823}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!136 &3544809171099342473
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6913723326018898823}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.50000024
  m_Height: 2
  m_Direction: 1
  m_Center: {x: 4.5474735e-13, y: 0, z: 0.000007629398}
--- !u!1 &6932348486116848188
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 5731796864543090382, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
  m_PrefabInstance: {fileID: 3325690817238656555}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6349198775541576308}
  - component: {fileID: 6719443464709749884}
  - component: {fileID: 6732568461217921299}
  m_Layer: 0
  m_Name: FrameCorner
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: **********
  m_IsActive: 1
--- !u!4 &6349198775541576308
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 3434745757639855058, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
  m_PrefabInstance: {fileID: 3325690817238656555}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6932348486116848188}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 3, y: 0, z: -3}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7716844785207754693}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &6719443464709749884
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4565872242631380402, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
  m_PrefabInstance: {fileID: 3325690817238656555}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6932348486116848188}
  m_Mesh: {fileID: 4969049306130400984, guid: 9625ac777d70ea648a19cdbbe169b438, type: 3}
--- !u!23 &6732568461217921299
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 3086446364843865080, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
  m_PrefabInstance: {fileID: 3325690817238656555}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6932348486116848188}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 257
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 735e5139c2106f54096a4ebbeea42ac7, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &6961607571721383167
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5491838911533581786}
  - component: {fileID: 1833460224384052505}
  m_Layer: 0
  m_Name: Decal Projector
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: **********
  m_IsActive: 1
--- !u!4 &5491838911533581786
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6961607571721383167}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0, y: 0.643, z: -0.173}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7222192614336574123}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1833460224384052505
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6961607571721383167}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0777d029ed3dffa4692f417d4aba19ca, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: -876546973899608171, guid: 94e8c7d345857324c86224e799f9883e, type: 3}
  m_DrawDistance: 1000
  m_FadeScale: 0.9
  m_StartAngleFade: 180
  m_EndAngleFade: 180
  m_UVScale: {x: 1, y: 1}
  m_UVBias: {x: 0, y: 0}
  m_DecalLayerMask: 1
  m_ScaleMode: 0
  m_Offset: {x: 0, y: 0, z: 0}
  m_Size: {x: 2, y: 2, z: 2}
  m_FadeFactor: 1
--- !u!1 &7138637777450928631
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3771897528352836944}
  m_Layer: 0
  m_Name: GameObject
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: **********
  m_IsActive: 1
--- !u!4 &3771897528352836944
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7138637777450928631}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7716844785207754693}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7198449784236246498
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 5383211035814758395, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
  m_PrefabInstance: {fileID: 3325690817238656555}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 922756289092827361}
  - component: {fileID: 7649629646860498320}
  - component: {fileID: 1915667017505703683}
  m_Layer: 0
  m_Name: FrameSide (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: **********
  m_IsActive: 1
--- !u!4 &922756289092827361
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 8166254447188671824, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
  m_PrefabInstance: {fileID: 3325690817238656555}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7198449784236246498}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0.70710677, z: -0, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: 3}
  m_LocalScale: {x: 1, y: 1, z: 2}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7716844785207754693}
  m_LocalEulerAnglesHint: {x: 0, y: -90, z: 0}
--- !u!33 &7649629646860498320
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 6361592146922430501, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
  m_PrefabInstance: {fileID: 3325690817238656555}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7198449784236246498}
  m_Mesh: {fileID: 2970638267290972382, guid: 1bf2e27b5c9b0d945b8fdbbf1165f828, type: 3}
--- !u!23 &1915667017505703683
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 7819549228457824122, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
  m_PrefabInstance: {fileID: 3325690817238656555}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7198449784236246498}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 257
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 735e5139c2106f54096a4ebbeea42ac7, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &7526998443185933264
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3273409636818177082}
  - component: {fileID: 7817651055067938019}
  - component: {fileID: 4973672246354644616}
  - component: {fileID: 8272871454805242207}
  m_Layer: 0
  m_Name: Cube (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: **********
  m_IsActive: 1
--- !u!4 &3273409636818177082
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7526998443185933264}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7075162569821107308}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 90}
--- !u!33 &7817651055067938019
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7526998443185933264}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &4973672246354644616
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7526998443185933264}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &8272871454805242207
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7526998443185933264}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1.0000005, y: 1, z: 1.0000005}
  m_Center: {x: 0, y: 0, z: 0.000007629398}
--- !u!1 &7978760745934428899
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 846161440402699366}
  m_Layer: 0
  m_Name: GameObject
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: **********
  m_IsActive: 1
--- !u!4 &846161440402699366
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7978760745934428899}
  serializedVersion: 2
  m_LocalRotation: {x: 0.27059805, y: 0.27059805, z: 0.6532815, w: 0.6532815}
  m_LocalPosition: {x: 0.36425102, y: 0.699999, z: -0.35838655}
  m_LocalScale: {x: 1, y: 0.10000002, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 455436811184286517}
  - {fileID: 685220620642898123}
  - {fileID: 7214431815756526463}
  m_Father: {fileID: 7745483497612582546}
  m_LocalEulerAnglesHint: {x: 0, y: 45, z: 90}
--- !u!1 &8166735027431545207
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 455436811184286517}
  - component: {fileID: 2720317606326906909}
  - component: {fileID: 3410859389175453526}
  - component: {fileID: 6911057219518859732}
  m_Layer: 0
  m_Name: Cube
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: **********
  m_IsActive: 1
--- !u!4 &455436811184286517
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8166735027431545207}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -0.50000006, y: 5, z: 0}
  m_LocalScale: {x: 10, y: 0.09999998, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 846161440402699366}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &2720317606326906909
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8166735027431545207}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &3410859389175453526
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8166735027431545207}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &6911057219518859732
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8166735027431545207}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1.0000005, y: 1, z: 1.0000005}
  m_Center: {x: 0, y: 0, z: 0.000007629398}
--- !u!1 &8368343432159209439
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4771084367819536138}
  - component: {fileID: 3434470169201640985}
  - component: {fileID: 7703696713344538664}
  - component: {fileID: 3059680910227579018}
  m_Layer: 0
  m_Name: Cube
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: **********
  m_IsActive: 1
--- !u!4 &4771084367819536138
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8368343432159209439}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -0.50000006, y: 5, z: 0}
  m_LocalScale: {x: 10, y: 0.09999998, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7075162569821107308}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &3434470169201640985
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8368343432159209439}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &7703696713344538664
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8368343432159209439}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &3059680910227579018
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8368343432159209439}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1.0000005, y: 1, z: 1.0000005}
  m_Center: {x: 0, y: 0, z: 0.000007629398}
--- !u!1 &8491013054880718421
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8981432136717117525}
  m_Layer: 0
  m_Name: PlatformDecals
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: **********
  m_IsActive: 1
--- !u!4 &8981432136717117525
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8491013054880718421}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: 0.7071068, z: -0, w: 0.7071068}
  m_LocalPosition: {x: -3, y: 0.5, z: -1}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7716844785207754693}
  - {fileID: 8013054990551701556}
  - {fileID: 4819597031127359389}
  - {fileID: 2426652723027145993}
  - {fileID: 1398908843447382318}
  - {fileID: 3380565141134661936}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8548803372513920657
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8805501651928800704}
  - component: {fileID: 4395624417314144801}
  - component: {fileID: 4157376181768652727}
  - component: {fileID: 8208711660336131541}
  m_Layer: 0
  m_Name: Cube (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: **********
  m_IsActive: 1
--- !u!4 &8805501651928800704
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8548803372513920657}
  serializedVersion: 2
  m_LocalRotation: {x: 0.5, y: -0.5, z: -0.5, w: 0.5}
  m_LocalPosition: {x: 0, y: 5, z: -0.49999982}
  m_LocalScale: {x: 10, y: 0.10000003, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4303619488855159463}
  m_LocalEulerAnglesHint: {x: 90, y: 0, z: 0}
--- !u!33 &4395624417314144801
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8548803372513920657}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &4157376181768652727
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8548803372513920657}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &8208711660336131541
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8548803372513920657}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1.0000005, y: 1, z: 1.0000005}
  m_Center: {x: 0, y: 0, z: 0.000007629398}
--- !u!1 &8653885611791951190
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 76818781994821566}
  - component: {fileID: 5368051131450407324}
  - component: {fileID: 2051568057735293601}
  - component: {fileID: 5955253135511284797}
  m_Layer: 0
  m_Name: Capsule (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &76818781994821566
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8653885611791951190}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.01, y: 0.008998871, z: -0.326}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7745483497612582546}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &5368051131450407324
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8653885611791951190}
  m_Mesh: {fileID: 10208, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &2051568057735293601
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8653885611791951190}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!136 &5955253135511284797
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8653885611791951190}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.50000024
  m_Height: 2
  m_Direction: 1
  m_Center: {x: 4.5474735e-13, y: 0, z: 0.000007629398}
--- !u!1 &8892854178715283499
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9080273441979808096}
  - component: {fileID: 4133210317176524426}
  - component: {fileID: 7191286640053085625}
  - component: {fileID: 4864680463697246975}
  m_Layer: 0
  m_Name: Cube (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: **********
  m_IsActive: 1
--- !u!4 &9080273441979808096
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8892854178715283499}
  serializedVersion: 2
  m_LocalRotation: {x: 0.5, y: -0.5, z: -0.5, w: 0.5}
  m_LocalPosition: {x: 0, y: 5, z: -0.49999982}
  m_LocalScale: {x: 10, y: 0.10000003, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6729831840931939383}
  m_LocalEulerAnglesHint: {x: 90, y: 0, z: 0}
--- !u!33 &4133210317176524426
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8892854178715283499}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &7191286640053085625
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8892854178715283499}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &4864680463697246975
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8892854178715283499}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1.0000005, y: 1, z: 1.0000005}
  m_Center: {x: 0, y: 0, z: 0.000007629398}
--- !u!1001 &404539405261097203
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 8981432136717117525}
    m_Modifications:
    - target: {fileID: 827753077012132199, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_StaticEditorFlags
      value: **********
      objectReference: {fileID: 0}
    - target: {fileID: 2244231380466416256, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_StaticEditorFlags
      value: **********
      objectReference: {fileID: 0}
    - target: {fileID: 3577***************, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_StaticEditorFlags
      value: **********
      objectReference: {fileID: 0}
    - target: {fileID: 3580610915091520067, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_text
      value: 'This decal uses triplanar projection to project a material in 3D space.
        It projects materials correctly onto any mesh that intersects the decal volume.


        It
        could be used to apply terrain materials on to other objects like rocks so
        that they blend in better with the terrain.'
      objectReference: {fileID: 0}
    - target: {fileID: 4995077095936682008, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_StaticEditorFlags
      value: **********
      objectReference: {fileID: 0}
    - target: {fileID: 5151998697988675438, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_LocalPosition.x
      value: 1.06
      objectReference: {fileID: 0}
    - target: {fileID: 5151998697988675438, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5151998697988675438, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_LocalPosition.z
      value: 5.2
      objectReference: {fileID: 0}
    - target: {fileID: 5151998697988675438, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 5151998697988675438, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5151998697988675438, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 5151998697988675438, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5151998697988675438, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5151998697988675438, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: -90
      objectReference: {fileID: 0}
    - target: {fileID: 5151998697988675438, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5185100815853684772, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_text
      value: Material Projection
      objectReference: {fileID: 0}
    - target: {fileID: 5758263007340075305, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_StaticEditorFlags
      value: **********
      objectReference: {fileID: 0}
    - target: {fileID: 6146513733896257349, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_StaticEditorFlags
      value: **********
      objectReference: {fileID: 0}
    - target: {fileID: 6772489030149727512, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_Name
      value: MatProj
      objectReference: {fileID: 0}
    - target: {fileID: 6772489030149727512, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_StaticEditorFlags
      value: **********
      objectReference: {fileID: 0}
    - target: {fileID: 7608448047173797114, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_text
      value: Material Projection
      objectReference: {fileID: 0}
    - target: {fileID: 7676843616018598530, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_StaticEditorFlags
      value: **********
      objectReference: {fileID: 0}
    - target: {fileID: 8290413756401460091, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_StaticEditorFlags
      value: **********
      objectReference: {fileID: 0}
    - target: {fileID: 8629496803597527292, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_StaticEditorFlags
      value: **********
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects:
    - targetCorrespondingSourceObject: {fileID: 209630298787057868, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      insertIndex: -1
      addedObject: {fileID: 4307562375208550975}
    - targetCorrespondingSourceObject: {fileID: 209630298787057868, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      insertIndex: -1
      addedObject: {fileID: 4303619488855159463}
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
--- !u!4 &537608235367061567 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 209630298787057868, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
  m_PrefabInstance: {fileID: 404539405261097203}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &4819597031127359389 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 5151998697988675438, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
  m_PrefabInstance: {fileID: 404539405261097203}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &1899071910888612704
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 8981432136717117525}
    m_Modifications:
    - target: {fileID: 823572905295939120, guid: 6c40ad478ff0e5a4c91d0b78f29e80e2, type: 3}
      propertyPath: m_LocalPosition.x
      value: -3
      objectReference: {fileID: 0}
    - target: {fileID: 823572905295939120, guid: 6c40ad478ff0e5a4c91d0b78f29e80e2, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 823572905295939120, guid: 6c40ad478ff0e5a4c91d0b78f29e80e2, type: 3}
      propertyPath: m_LocalPosition.z
      value: -1
      objectReference: {fileID: 0}
    - target: {fileID: 823572905295939120, guid: 6c40ad478ff0e5a4c91d0b78f29e80e2, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 823572905295939120, guid: 6c40ad478ff0e5a4c91d0b78f29e80e2, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 823572905295939120, guid: 6c40ad478ff0e5a4c91d0b78f29e80e2, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 823572905295939120, guid: 6c40ad478ff0e5a4c91d0b78f29e80e2, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 823572905295939120, guid: 6c40ad478ff0e5a4c91d0b78f29e80e2, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 823572905295939120, guid: 6c40ad478ff0e5a4c91d0b78f29e80e2, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 90
      objectReference: {fileID: 0}
    - target: {fileID: 823572905295939120, guid: 6c40ad478ff0e5a4c91d0b78f29e80e2, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1137507590067690614, guid: 6c40ad478ff0e5a4c91d0b78f29e80e2, type: 3}
      propertyPath: m_text
      value: Decals
      objectReference: {fileID: 0}
    - target: {fileID: 1537572312732349544, guid: 6c40ad478ff0e5a4c91d0b78f29e80e2, type: 3}
      propertyPath: m_StaticEditorFlags
      value: **********
      objectReference: {fileID: 0}
    - target: {fileID: 1995278512336704064, guid: 6c40ad478ff0e5a4c91d0b78f29e80e2, type: 3}
      propertyPath: m_StaticEditorFlags
      value: **********
      objectReference: {fileID: 0}
    - target: {fileID: 2759006676346111776, guid: 6c40ad478ff0e5a4c91d0b78f29e80e2, type: 3}
      propertyPath: m_text
      value: "Decals allow you to apply local material modifications to specific
        places in the world. You might think of things like applying graffiti tags
        to a wall or scattering fallen leaves below a tree. But decals can be used
        for a lot more. In these examples, we see decals making things look wet,
        making surfaces appear to have flowing water across them, projecting water
        caustics, and blending specific materials onto other objects.\r\n\r\nDecals
        are available to use in both HDRP and URP, but they need to be enabled in
        both render pipelines. To use decals, see the documentation in both HDRP
        and URP.\r"
      objectReference: {fileID: 0}
    - target: {fileID: 4135125235891897624, guid: 6c40ad478ff0e5a4c91d0b78f29e80e2, type: 3}
      propertyPath: m_StaticEditorFlags
      value: **********
      objectReference: {fileID: 0}
    - target: {fileID: 6270412221303966579, guid: 6c40ad478ff0e5a4c91d0b78f29e80e2, type: 3}
      propertyPath: m_Name
      value: InfoPanel
      objectReference: {fileID: 0}
    - target: {fileID: 6270412221303966579, guid: 6c40ad478ff0e5a4c91d0b78f29e80e2, type: 3}
      propertyPath: m_StaticEditorFlags
      value: **********
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 6c40ad478ff0e5a4c91d0b78f29e80e2, type: 3}
--- !u!1001 &3325690817238656555
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 8981432136717117525}
    m_Modifications:
    - target: {fileID: 50457568970307013, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
      propertyPath: m_Name
      value: Platform
      objectReference: {fileID: 0}
    - target: {fileID: 50457568970307013, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
      propertyPath: m_StaticEditorFlags
      value: **********
      objectReference: {fileID: 0}
    - target: {fileID: 93298385648041818, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
      propertyPath: m_StaticEditorFlags
      value: **********
      objectReference: {fileID: 0}
    - target: {fileID: 1049764582655622063, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
      propertyPath: m_StaticEditorFlags
      value: **********
      objectReference: {fileID: 0}
    - target: {fileID: 1512132155333820600, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
      propertyPath: m_StaticEditorFlags
      value: **********
      objectReference: {fileID: 0}
    - target: {fileID: 3250075270326530992, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
      propertyPath: m_StaticEditorFlags
      value: **********
      objectReference: {fileID: 0}
    - target: {fileID: 3753311242483336413, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
      propertyPath: m_StaticEditorFlags
      value: **********
      objectReference: {fileID: 0}
    - target: {fileID: 5383211035814758395, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
      propertyPath: m_StaticEditorFlags
      value: **********
      objectReference: {fileID: 0}
    - target: {fileID: 5731796864543090382, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
      propertyPath: m_StaticEditorFlags
      value: **********
      objectReference: {fileID: 0}
    - target: {fileID: 6915846320344479465, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6915846320344479465, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6915846320344479465, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6915846320344479465, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6915846320344479465, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6915846320344479465, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6915846320344479465, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6915846320344479465, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6915846320344479465, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6915846320344479465, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7726019937898060624, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
      propertyPath: m_StaticEditorFlags
      value: **********
      objectReference: {fileID: 0}
    - target: {fileID: 7817475782026850255, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
      propertyPath: m_StaticEditorFlags
      value: **********
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects:
    - targetCorrespondingSourceObject: {fileID: 6915846320344479465, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
      insertIndex: -1
      addedObject: {fileID: 3771897528352836944}
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 60d2779c2d5586e418729abcbb39a213, type: 3}
--- !u!1001 &6059170719382021696
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 8981432136717117525}
    m_Modifications:
    - target: {fileID: 827753077012132199, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_StaticEditorFlags
      value: **********
      objectReference: {fileID: 0}
    - target: {fileID: 2244231380466416256, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_StaticEditorFlags
      value: **********
      objectReference: {fileID: 0}
    - target: {fileID: 3577***************, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_StaticEditorFlags
      value: **********
      objectReference: {fileID: 0}
    - target: {fileID: 3580610915091520067, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_text
      value: This decal creates the appearance of flowing water across whatever surfaces
        are inside the decal. It can be used on the banks of streams and around waterfalls
        to support the appearance of water flowing. With material parameters, you
        can control the speed of the water flow, the opacity of both the wetness
        and the water, and the strength of the flowing water normals.
      objectReference: {fileID: 0}
    - target: {fileID: 4995077095936682008, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_StaticEditorFlags
      value: **********
      objectReference: {fileID: 0}
    - target: {fileID: 5151998697988675438, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_LocalPosition.x
      value: 1.06
      objectReference: {fileID: 0}
    - target: {fileID: 5151998697988675438, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5151998697988675438, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_LocalPosition.z
      value: 2.13
      objectReference: {fileID: 0}
    - target: {fileID: 5151998697988675438, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 5151998697988675438, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5151998697988675438, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 5151998697988675438, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5151998697988675438, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5151998697988675438, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: -90
      objectReference: {fileID: 0}
    - target: {fileID: 5151998697988675438, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5185100815853684772, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_text
      value: Running Water
      objectReference: {fileID: 0}
    - target: {fileID: 5758263007340075305, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_StaticEditorFlags
      value: **********
      objectReference: {fileID: 0}
    - target: {fileID: 6146513733896257349, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_StaticEditorFlags
      value: **********
      objectReference: {fileID: 0}
    - target: {fileID: 6772489030149727512, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_Name
      value: RunningWater
      objectReference: {fileID: 0}
    - target: {fileID: 6772489030149727512, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_StaticEditorFlags
      value: **********
      objectReference: {fileID: 0}
    - target: {fileID: 7608448047173797114, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_text
      value: Running Water
      objectReference: {fileID: 0}
    - target: {fileID: 7676843616018598530, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_StaticEditorFlags
      value: **********
      objectReference: {fileID: 0}
    - target: {fileID: 8290413756401460091, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_StaticEditorFlags
      value: **********
      objectReference: {fileID: 0}
    - target: {fileID: 8629496803597527292, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_StaticEditorFlags
      value: **********
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects:
    - targetCorrespondingSourceObject: {fileID: 209630298787057868, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      insertIndex: -1
      addedObject: {fileID: 7358328783974989560}
    - targetCorrespondingSourceObject: {fileID: 209630298787057868, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      insertIndex: -1
      addedObject: {fileID: 8323059998458372410}
    - targetCorrespondingSourceObject: {fileID: 209630298787057868, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      insertIndex: -1
      addedObject: {fileID: 7075162569821107308}
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
--- !u!4 &1398908843447382318 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 5151998697988675438, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
  m_PrefabInstance: {fileID: 6059170719382021696}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &6268656707265246860 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 209630298787057868, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
  m_PrefabInstance: {fileID: 6059170719382021696}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &7409160600718647911
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 8981432136717117525}
    m_Modifications:
    - target: {fileID: 827753077012132199, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_StaticEditorFlags
      value: **********
      objectReference: {fileID: 0}
    - target: {fileID: 2244231380466416256, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_StaticEditorFlags
      value: **********
      objectReference: {fileID: 0}
    - target: {fileID: 3577***************, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_StaticEditorFlags
      value: **********
      objectReference: {fileID: 0}
    - target: {fileID: 3580610915091520067, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_text
      value: "When light shines through rippling water, the water warps and focuses
        the light, casting really interesting rippling patterns on surfaces under
        the water.  This shader creates these rippling caustic patterns. If you place
        decals using this shader under your water planes, you\u2019ll get projected
        caustics that imitate the behavior of light shining through the water."
      objectReference: {fileID: 0}
    - target: {fileID: 4995077095936682008, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_StaticEditorFlags
      value: **********
      objectReference: {fileID: 0}
    - target: {fileID: 5151998697988675438, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_LocalPosition.x
      value: -2.8
      objectReference: {fileID: 0}
    - target: {fileID: 5151998697988675438, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5151998697988675438, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_LocalPosition.z
      value: 5.2
      objectReference: {fileID: 0}
    - target: {fileID: 5151998697988675438, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 5151998697988675438, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5151998697988675438, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 5151998697988675438, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5151998697988675438, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5151998697988675438, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: -90
      objectReference: {fileID: 0}
    - target: {fileID: 5151998697988675438, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5185100815853684772, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_text
      value: Water Caustics
      objectReference: {fileID: 0}
    - target: {fileID: 5758263007340075305, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_StaticEditorFlags
      value: **********
      objectReference: {fileID: 0}
    - target: {fileID: 6146513733896257349, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_StaticEditorFlags
      value: **********
      objectReference: {fileID: 0}
    - target: {fileID: 6772489030149727512, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_Name
      value: WaterCaustics
      objectReference: {fileID: 0}
    - target: {fileID: 6772489030149727512, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_StaticEditorFlags
      value: **********
      objectReference: {fileID: 0}
    - target: {fileID: 7608448047173797114, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_text
      value: Water Caustics
      objectReference: {fileID: 0}
    - target: {fileID: 7676843616018598530, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_StaticEditorFlags
      value: **********
      objectReference: {fileID: 0}
    - target: {fileID: 8290413756401460091, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_StaticEditorFlags
      value: **********
      objectReference: {fileID: 0}
    - target: {fileID: 8629496803597527292, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_StaticEditorFlags
      value: **********
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects:
    - targetCorrespondingSourceObject: {fileID: 209630298787057868, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      insertIndex: -1
      addedObject: {fileID: 5491838911533581786}
    - targetCorrespondingSourceObject: {fileID: 209630298787057868, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      insertIndex: -1
      addedObject: {fileID: 6729831840931939383}
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
--- !u!4 &2426652723027145993 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 5151998697988675438, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
  m_PrefabInstance: {fileID: 7409160600718647911}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &7222192614336574123 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 209630298787057868, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
  m_PrefabInstance: {fileID: 7409160600718647911}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &7608195577280826974
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 8981432136717117525}
    m_Modifications:
    - target: {fileID: 827753077012132199, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_StaticEditorFlags
      value: **********
      objectReference: {fileID: 0}
    - target: {fileID: 2244231380466416256, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_StaticEditorFlags
      value: **********
      objectReference: {fileID: 0}
    - target: {fileID: 3577***************, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_StaticEditorFlags
      value: **********
      objectReference: {fileID: 0}
    - target: {fileID: 3580610915091520067, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_text
      value: The wetness decal makes surfaces look wet by darkening color and increasing
        smoothness. It uses very simple math and no texture samples so it is very
        performance efficient. It can be used along the banks of bodies of water
        to better integrate the water with the environment.
      objectReference: {fileID: 0}
    - target: {fileID: 4995077095936682008, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_StaticEditorFlags
      value: **********
      objectReference: {fileID: 0}
    - target: {fileID: 5151998697988675438, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_LocalPosition.x
      value: -2.8
      objectReference: {fileID: 0}
    - target: {fileID: 5151998697988675438, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5151998697988675438, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_LocalPosition.z
      value: 2.13
      objectReference: {fileID: 0}
    - target: {fileID: 5151998697988675438, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 5151998697988675438, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5151998697988675438, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 5151998697988675438, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5151998697988675438, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5151998697988675438, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: -90
      objectReference: {fileID: 0}
    - target: {fileID: 5151998697988675438, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5185100815853684772, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_text
      value: 'Water Wetness

'
      objectReference: {fileID: 0}
    - target: {fileID: 5758263007340075305, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_StaticEditorFlags
      value: **********
      objectReference: {fileID: 0}
    - target: {fileID: 6146513733896257349, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_StaticEditorFlags
      value: **********
      objectReference: {fileID: 0}
    - target: {fileID: 6772489030149727512, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_Name
      value: WaterWetness
      objectReference: {fileID: 0}
    - target: {fileID: 6772489030149727512, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_StaticEditorFlags
      value: **********
      objectReference: {fileID: 0}
    - target: {fileID: 7608448047173797114, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_text
      value: Water Wetness
      objectReference: {fileID: 0}
    - target: {fileID: 7676843616018598530, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_StaticEditorFlags
      value: **********
      objectReference: {fileID: 0}
    - target: {fileID: 8290413756401460091, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_StaticEditorFlags
      value: **********
      objectReference: {fileID: 0}
    - target: {fileID: 8629496803597527292, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      propertyPath: m_StaticEditorFlags
      value: **********
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects:
    - targetCorrespondingSourceObject: {fileID: 209630298787057868, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      insertIndex: -1
      addedObject: {fileID: 4558290094239230542}
    - targetCorrespondingSourceObject: {fileID: 209630298787057868, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      insertIndex: -1
      addedObject: {fileID: 76818781994821566}
    - targetCorrespondingSourceObject: {fileID: 209630298787057868, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
      insertIndex: -1
      addedObject: {fileID: 846161440402699366}
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
--- !u!4 &3380565141134661936 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 5151998697988675438, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
  m_PrefabInstance: {fileID: 7608195577280826974}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &7745483497612582546 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 209630298787057868, guid: 217c64a5792b3cd45bbca2d0a6a3e16c, type: 3}
  m_PrefabInstance: {fileID: 7608195577280826974}
  m_PrefabAsset: {fileID: 0}
