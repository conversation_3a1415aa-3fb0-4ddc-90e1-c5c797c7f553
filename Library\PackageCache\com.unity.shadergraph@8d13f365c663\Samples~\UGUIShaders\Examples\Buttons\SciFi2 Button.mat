%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: SciFi2 Button
  m_Shader: {fileID: -6465566751694194690, guid: 55cf91c206640a84f855ae76a49e0eda, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords: []
  m_InvalidKeywords: []
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _AquaButton_b1cf5d0c0b6b492db0a0ccc3d369b5cf_Label_2407708783_Texture2D:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _Active: 1
    - _ColorMask: 15
    - _Grid_Tiles: 24
    - _Pressed: 0
    - _Selected: 0
    - _Stencil: 0
    - _StencilComp: 8
    - _StencilOp: 0
    - _StencilReadMask: 255
    - _StencilWriteMask: 255
    - _Tilt: -0.3
    - _UIMaskSoftnessX: 1
    - _UIMaskSoftnessY: 1
    m_Colors:
    - _ClipRect: {r: 0, g: 0, b: 0, a: 0}
    - _Color: {r: 0, g: 0.25098038, b: 1, a: 0}
    - _Dimensions: {r: 0.8, g: 0.6, b: 0, a: 0}
    - _WidthHeight: {r: 300, g: 100, b: 0, a: 0}
    - _Width_Height: {r: 300, g: 100, b: 0, a: 0}
  m_BuildTextureStacks: []
  m_AllowLocking: 1
