%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 5
    smallestHole: 0.25
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 10
  m_Fog: 0
  m_FogColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_FogMode: 3
  m_FogDensity: 0.01
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientSkyColor: {r: 0.212, g: 0.227, b: 0.259, a: 1}
  m_AmbientEquatorColor: {r: 0.114, g: 0.125, b: 0.133, a: 1}
  m_AmbientGroundColor: {r: 0.047, g: 0.043, b: 0.035, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 0
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 0}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 1
  m_DefaultReflectionResolution: 128
  m_ReflectionBounces: 1
  m_ReflectionIntensity: 1
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 0}
  m_UseRadianceAmbientProbe: 0
--- !u!157 &3
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 13
  m_BakeOnSceneLoad: 0
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_EnvironmentLightingMode: 0
    m_EnableBakedLightmaps: 1
    m_EnableRealtimeLightmaps: 0
  m_LightmapEditorSettings:
    serializedVersion: 12
    m_Resolution: 2
    m_BakeResolution: 40
    m_AtlasSize: 1024
    m_AO: 0
    m_AOMaxDistance: 1
    m_CompAOExponent: 1
    m_CompAOExponentDirect: 0
    m_ExtractAmbientOcclusion: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 0}
    m_LightmapsBakeMode: 1
    m_TextureCompression: 1
    m_ReflectionCompression: 2
    m_MixedBakeMode: 2
    m_BakeBackend: 1
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 32
    m_PVRSampleCount: 512
    m_PVRBounces: 2
    m_PVREnvironmentSampleCount: 256
    m_PVREnvironmentReferencePointCount: 2048
    m_PVRFilteringMode: 1
    m_PVRDenoiserTypeDirect: 1
    m_PVRDenoiserTypeIndirect: 1
    m_PVRDenoiserTypeAO: 1
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVREnvironmentMIS: 1
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 5
    m_PVRFilteringGaussRadiusAO: 2
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ExportTrainingData: 0
    m_TrainingDataDestination: TrainingData
    m_LightProbeSampleCountMultiplier: 4
  m_LightingDataAsset: {fileID: 112000000, guid: fc028136fadc2724e94ad722d3c3eb4e, type: 2}
  m_LightingSettings: {fileID: 4890085278179872738, guid: d5eb2309f8ea36e499f334148b73a327, type: 2}
--- !u!196 &4
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 3
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666667
    manualTileSize: 0
    tileSize: 256
    buildHeightMesh: 0
    maxJobWorkers: 0
    preserveTilesOutsideBounds: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!1001 &73290142
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 1649024369}
    m_Modifications:
    - target: {fileID: 92038933656319273, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_Name
      value: Tint Blue Slider
      objectReference: {fileID: 0}
    - target: {fileID: 905015512013034897, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_text
      value: Tint / Blue
      objectReference: {fileID: 0}
    - target: {fileID: 905015512013034897, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_fontStyle
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 905015512013034897, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_fontColor.b
      value: 0.72955966
      objectReference: {fileID: 0}
    - target: {fileID: 905015512013034897, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_fontColor.g
      value: 0.72955966
      objectReference: {fileID: 0}
    - target: {fileID: 905015512013034897, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_fontColor.r
      value: 0.72955966
      objectReference: {fileID: 0}
    - target: {fileID: 905015512013034897, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_fontColor32.rgba
      value: 4290427578
      objectReference: {fileID: 0}
    - target: {fileID: 6753850657890399353, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_Name
      value: Tint Blue Slider
      objectReference: {fileID: 0}
    - target: {fileID: 7194105814776850979, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7194105814776850979, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_Pivot.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_Pivot.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_AnchorMax.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8561477015106430877, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8561477015106430877, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
--- !u!224 &73290143 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
  m_PrefabInstance: {fileID: 73290142}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &73290144 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 1077107949267788258, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
  m_PrefabInstance: {fileID: 73290142}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 67db9e8f0e2ae9c40bc1e2b64352a6b4, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1001 &114155306
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 1649024369}
    m_Modifications:
    - target: {fileID: 92038933656319273, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_Name
      value: Hexagon Tiles Slider
      objectReference: {fileID: 0}
    - target: {fileID: 905015512013034897, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_text
      value: Hexagon Tiles
      objectReference: {fileID: 0}
    - target: {fileID: 905015512013034897, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_fontStyle
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 905015512013034897, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_fontColor.b
      value: 0.72955966
      objectReference: {fileID: 0}
    - target: {fileID: 905015512013034897, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_fontColor.g
      value: 0.72955966
      objectReference: {fileID: 0}
    - target: {fileID: 905015512013034897, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_fontColor.r
      value: 0.72955966
      objectReference: {fileID: 0}
    - target: {fileID: 905015512013034897, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_fontColor32.rgba
      value: 4290427578
      objectReference: {fileID: 0}
    - target: {fileID: 1077107949267788258, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_Value
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1077107949267788258, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_MaxValue
      value: 10
      objectReference: {fileID: 0}
    - target: {fileID: 1077107949267788258, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_MinValue
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6753850657890399353, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_Name
      value: Hexagon Tiles Slider
      objectReference: {fileID: 0}
    - target: {fileID: 7194105814776850979, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7194105814776850979, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_Pivot.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_Pivot.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_AnchorMax.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8561477015106430877, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8561477015106430877, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
--- !u!224 &114155307 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
  m_PrefabInstance: {fileID: 114155306}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &114155308 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 1077107949267788258, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
  m_PrefabInstance: {fileID: 114155306}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 67db9e8f0e2ae9c40bc1e2b64352a6b4, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &142244313
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 142244314}
  - component: {fileID: 142244316}
  - component: {fileID: 142244315}
  m_Layer: 5
  m_Name: Text (TMP)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &142244314
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 142244313}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1231661143}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &142244315
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 142244313}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: Button
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_sharedMaterial: {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 38
  m_fontSizeBase: 38
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &142244316
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 142244313}
  m_CullTransparentMesh: 1
--- !u!1 &153029046
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 153029047}
  - component: {fileID: 153029051}
  - component: {fileID: 153029050}
  m_Layer: 5
  m_Name: FancyLoading
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &153029047
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 153029046}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1900708143}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 180, y: 180}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &153029050
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 153029046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: -876546973899608171, guid: b3bbeec1e373edd448bf6e8f564d41a8, type: 3}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &153029051
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 153029046}
  m_CullTransparentMesh: 1
--- !u!114 &199872146 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 1077107949267788258, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
  m_PrefabInstance: {fileID: 2236064079864338391}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 67db9e8f0e2ae9c40bc1e2b64352a6b4, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &308996964
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 308996965}
  - component: {fileID: 308996967}
  - component: {fileID: 308996966}
  m_Layer: 5
  m_Name: Fill
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &308996965
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 308996964}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 619019711}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 10, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &308996966
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 308996964}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10905, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &308996967
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 308996964}
  m_CullTransparentMesh: 1
--- !u!1 &333554811
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 333554812}
  - component: {fileID: 333554816}
  - component: {fileID: 333554815}
  - component: {fileID: 333554814}
  - component: {fileID: 333554817}
  m_Layer: 5
  m_Name: MeterAqua (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &333554812
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 333554811}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 510766279}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0.5}
  m_AnchorMax: {x: 0.2, y: 0.5}
  m_AnchoredPosition: {x: 47.5, y: 0}
  m_SizeDelta: {x: -105, y: 80}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &333554814
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 333554811}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a0fdf6750a7499e4faef09a8cc8ddfde, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &333554815
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 333554811}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 2100000, guid: a16742ff981b9814d96283c1cff28880, type: 2}
  m_Color: {r: 0.0141509175, g: 0.5704708, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &333554816
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 333554811}
  m_CullTransparentMesh: 1
--- !u!114 &333554817
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 333554811}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 33236b32a07827145b40a42b9033672e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _value: {x: 0, y: 1}
  _direction: 0
  defaultMaterial: {fileID: -876546973899608171, guid: 671c5a7d7c5e6bc40bbae6edd1955c32, type: 3}
--- !u!1 &362484447 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 5597060975141118542, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
  m_PrefabInstance: {fileID: 1404563808}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &380862419
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 380862420}
  - component: {fileID: 380862423}
  - component: {fileID: 380862422}
  - component: {fileID: 380862421}
  m_Layer: 5
  m_Name: ButtonSimple (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &380862420
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 380862419}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1501147054}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0.5}
  m_AnchorMax: {x: 0.2, y: 0.5}
  m_AnchoredPosition: {x: 47.5, y: 0}
  m_SizeDelta: {x: -105, y: 80}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &380862421
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 380862419}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a0fdf6750a7499e4faef09a8cc8ddfde, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &380862422
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 380862419}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 2100000, guid: 852cbe2a621ecc64a96412d473138ac8, type: 2}
  m_Color: {r: 0.3443396, g: 1, b: 0.40522814, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &380862423
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 380862419}
  m_CullTransparentMesh: 1
--- !u!1 &382653529
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 382653530}
  - component: {fileID: 382653532}
  - component: {fileID: 382653531}
  m_Layer: 5
  m_Name: Text (TMP)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &382653530
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 382653529}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1840198704}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &382653531
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 382653529}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: Button
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_sharedMaterial: {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 38
  m_fontSizeBase: 38
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &382653532
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 382653529}
  m_CullTransparentMesh: 1
--- !u!1 &392127262
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 392127263}
  - component: {fileID: 392127269}
  - component: {fileID: 392127268}
  - component: {fileID: 392127270}
  - component: {fileID: 392127271}
  m_Layer: 5
  m_Name: ButtonSciFi2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &392127263
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 392127262}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 490046112}
  m_Father: {fileID: 913561108}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 300, y: 100}
  m_Pivot: {x: 0, y: 0.5}
--- !u!114 &392127268
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 392127262}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 2100000, guid: 88492887ebeaaf34eb53c483b8c33224, type: 2}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &392127269
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 392127262}
  m_CullTransparentMesh: 1
--- !u!114 &392127270
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 392127262}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 457b592a913195e4b8cb0804abbe40de, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 0
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 392127268}
  onClick:
    m_PersistentCalls:
      m_Calls: []
  onStateChanged:
    m_PersistentCalls:
      m_Calls: []
  defaultMaterial: {fileID: -876546973899608171, guid: 9166601a958188e4caa2b1418d72baaf, type: 3}
--- !u!114 &392127271
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 392127262}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a0fdf6750a7499e4faef09a8cc8ddfde, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1001 &402657851
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 1395715298}
    m_Modifications:
    - target: {fileID: 3022974012137700811, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_Enabled
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3022974012137700811, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_AdditionalShaderChannelsFlag
      value: 25
      objectReference: {fileID: 0}
    - target: {fileID: 3097647832025268333, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_text
      value: Interactables are buttons, sliders, and other elements that the player
        can adjust. Try sliding the elements in the top box and hovering over or
        pressing the buttons in the second box to see how they interact. All of these
        states are defined in Shader Graph.
      objectReference: {fileID: 0}
    - target: {fileID: 7513160448566667922, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_text
      value: Interactables
      objectReference: {fileID: 0}
    - target: {fileID: 7513160448566667922, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_fontSize
      value: 54
      objectReference: {fileID: 0}
    - target: {fileID: 7513160448566667922, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_fontStyle
      value: 17
      objectReference: {fileID: 0}
    - target: {fileID: 7513160448566667922, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_fontSizeBase
      value: 54
      objectReference: {fileID: 0}
    - target: {fileID: 8208681277822599831, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_Name
      value: Page - Interactables
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_Pivot.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_Pivot.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_AnchorMax.x
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_SizeDelta.x
      value: -420
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_SizeDelta.y
      value: -40
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 190
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8405060111705126841, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_text
      value: You can interact with the buttons and sliders on this page by clicking
        and dragging on them.
      objectReference: {fileID: 0}
    - target: {fileID: 8405060111705126841, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_fontSize
      value: 30
      objectReference: {fileID: 0}
    - target: {fileID: 8405060111705126841, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_fontSizeBase
      value: 30
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects:
    - targetCorrespondingSourceObject: {fileID: 4262581316619277117, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      insertIndex: -1
      addedObject: {fileID: 1999474827}
    - targetCorrespondingSourceObject: {fileID: 4262581316619277117, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      insertIndex: -1
      addedObject: {fileID: 913561108}
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: 5597060975141118542, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      insertIndex: -1
      addedObject: {fileID: 1005163286}
  m_SourcePrefab: {fileID: 100100000, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
--- !u!224 &402657852 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
  m_PrefabInstance: {fileID: 402657851}
  m_PrefabAsset: {fileID: 0}
--- !u!223 &402657853 stripped
Canvas:
  m_CorrespondingSourceObject: {fileID: 3022974012137700811, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
  m_PrefabInstance: {fileID: 402657851}
  m_PrefabAsset: {fileID: 0}
--- !u!224 &471393352 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
  m_PrefabInstance: {fileID: 1112707155501840389}
  m_PrefabAsset: {fileID: 0}
--- !u!223 &471393354 stripped
Canvas:
  m_CorrespondingSourceObject: {fileID: 3022974012137700811, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
  m_PrefabInstance: {fileID: 1112707155501840389}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &487747952 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 5597060975141118542, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
  m_PrefabInstance: {fileID: 402657851}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &490046111
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 490046112}
  - component: {fileID: 490046114}
  - component: {fileID: 490046113}
  m_Layer: 5
  m_Name: Text (TMP)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &490046112
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 490046111}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 392127263}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &490046113
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 490046111}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: Button
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_sharedMaterial: {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294954185
  m_fontColor: {r: 0.7877358, g: 0.7985572, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 38
  m_fontSizeBase: 38
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &490046114
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 490046111}
  m_CullTransparentMesh: 1
--- !u!1 &510766278
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 510766279}
  - component: {fileID: 510766282}
  - component: {fileID: 510766281}
  - component: {fileID: 510766280}
  m_Layer: 5
  m_Name: Upper Shelf
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &510766279
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 510766278}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 6}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 333554812}
  - {fileID: 895525034}
  - {fileID: 1982639599}
  m_Father: {fileID: 2097437749}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &510766280
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 510766278}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a0fdf6750a7499e4faef09a8cc8ddfde, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &510766281
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 510766278}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 2100000, guid: f0eb4d347c180c14ca49bca838efbe8a, type: 2}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &510766282
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 510766278}
  m_CullTransparentMesh: 1
--- !u!224 &531894451 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 1203758887085499046, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
  m_PrefabInstance: {fileID: 1773589103853002916}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &619019710
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 619019711}
  m_Layer: 5
  m_Name: Fill Area
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &619019711
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 619019710}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 308996965}
  m_Father: {fileID: 666825513}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0.25}
  m_AnchorMax: {x: 1, y: 0.75}
  m_AnchoredPosition: {x: -5, y: 0}
  m_SizeDelta: {x: -20, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1001 &654503345
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 1649024369}
    m_Modifications:
    - target: {fileID: 92038933656319273, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_Name
      value: Blur Slider
      objectReference: {fileID: 0}
    - target: {fileID: 905015512013034897, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_text
      value: Blur
      objectReference: {fileID: 0}
    - target: {fileID: 905015512013034897, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_fontStyle
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 905015512013034897, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_fontColor.b
      value: 0.72955966
      objectReference: {fileID: 0}
    - target: {fileID: 905015512013034897, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_fontColor.g
      value: 0.72955966
      objectReference: {fileID: 0}
    - target: {fileID: 905015512013034897, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_fontColor.r
      value: 0.72955966
      objectReference: {fileID: 0}
    - target: {fileID: 905015512013034897, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_fontColor32.rgba
      value: 4290427578
      objectReference: {fileID: 0}
    - target: {fileID: 6753850657890399353, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_Name
      value: Blur Slider
      objectReference: {fileID: 0}
    - target: {fileID: 7194105814776850979, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7194105814776850979, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_Pivot.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_Pivot.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_AnchorMax.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8561477015106430877, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8561477015106430877, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
--- !u!224 &654503346 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
  m_PrefabInstance: {fileID: 654503345}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &654503347 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 1077107949267788258, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
  m_PrefabInstance: {fileID: 654503345}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 67db9e8f0e2ae9c40bc1e2b64352a6b4, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &666825512
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 666825513}
  - component: {fileID: 666825515}
  - component: {fileID: 666825514}
  m_Layer: 5
  m_Name: Slider
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &666825513
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 666825512}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1069439670}
  - {fileID: 619019711}
  - {fileID: 1848559309}
  m_Father: {fileID: 1404563810}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 0}
  m_AnchoredPosition: {x: 0, y: 30}
  m_SizeDelta: {x: 0, y: 20}
  m_Pivot: {x: 0.5, y: 0}
--- !u!114 &666825514
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 666825512}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 306cc8c2b49d7114eaa3623786fc2126, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreLayout: 1
  m_MinWidth: -1
  m_MinHeight: -1
  m_PreferredWidth: -1
  m_PreferredHeight: -1
  m_FlexibleWidth: -1
  m_FlexibleHeight: -1
  m_LayoutPriority: 1
--- !u!114 &666825515
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 666825512}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 67db9e8f0e2ae9c40bc1e2b64352a6b4, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 1642025958}
  m_FillRect: {fileID: 308996965}
  m_HandleRect: {fileID: 1642025957}
  m_Direction: 0
  m_MinValue: 0
  m_MaxValue: 1
  m_WholeNumbers: 0
  m_Value: 0.519
  m_OnValueChanged:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 1096159068}
        m_TargetAssemblyTypeName: Unity.UI.Shaders.Sample.Meter, Unity.UI.Shaders.Sample
        m_MethodName: set_Value
        m_Mode: 0
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 1
      - m_Target: {fileID: 1924907268}
        m_TargetAssemblyTypeName: Unity.UI.Shaders.Sample.Meter, Unity.UI.Shaders.Sample
        m_MethodName: set_Value
        m_Mode: 0
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 1
      - m_Target: {fileID: 1972633166}
        m_TargetAssemblyTypeName: Unity.UI.Shaders.Sample.Meter, Unity.UI.Shaders.Sample
        m_MethodName: set_Value
        m_Mode: 0
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 1
      - m_Target: {fileID: 1134473707}
        m_TargetAssemblyTypeName: Unity.UI.Shaders.Sample.Meter, Unity.UI.Shaders.Sample
        m_MethodName: set_Value
        m_Mode: 0
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 1
--- !u!1 &721419996
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 721419997}
  - component: {fileID: 721420000}
  - component: {fileID: 721419999}
  - component: {fileID: 721419998}
  - component: {fileID: 721420002}
  m_Layer: 5
  m_Name: MeterAqua
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &721419997
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 721419996}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1999474827}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 0.5, y: 1}
  m_AnchoredPosition: {x: 100, y: -40}
  m_SizeDelta: {x: -100, y: 100}
  m_Pivot: {x: 0, y: 1}
--- !u!114 &721419998
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 721419996}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a0fdf6750a7499e4faef09a8cc8ddfde, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &721419999
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 721419996}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 2100000, guid: a16742ff981b9814d96283c1cff28880, type: 2}
  m_Color: {r: 0.0141509175, g: 0.5704708, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &721420000
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 721419996}
  m_CullTransparentMesh: 1
--- !u!114 &721420002
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 721419996}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fb201a23cacc2a7479c31ac9c37407c5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 0
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 721419999}
  _direction: 0
  _value: 0.5
  onStateChanged:
    m_PersistentCalls:
      m_Calls: []
  onValueChanged:
    m_PersistentCalls:
      m_Calls: []
  defaultMaterial: {fileID: 0}
--- !u!1001 &872504257
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 1649024369}
    m_Modifications:
    - target: {fileID: 92038933656319273, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_Name
      value: Tint Green Slider
      objectReference: {fileID: 0}
    - target: {fileID: 905015512013034897, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_text
      value: Tint / Green
      objectReference: {fileID: 0}
    - target: {fileID: 905015512013034897, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_fontStyle
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 905015512013034897, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_fontColor.b
      value: 0.72955966
      objectReference: {fileID: 0}
    - target: {fileID: 905015512013034897, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_fontColor.g
      value: 0.72955966
      objectReference: {fileID: 0}
    - target: {fileID: 905015512013034897, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_fontColor.r
      value: 0.72955966
      objectReference: {fileID: 0}
    - target: {fileID: 905015512013034897, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_fontColor32.rgba
      value: 4290427578
      objectReference: {fileID: 0}
    - target: {fileID: 6753850657890399353, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_Name
      value: Tint Green Slider
      objectReference: {fileID: 0}
    - target: {fileID: 7194105814776850979, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7194105814776850979, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_Pivot.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_Pivot.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_AnchorMax.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8561477015106430877, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8561477015106430877, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
--- !u!224 &872504258 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
  m_PrefabInstance: {fileID: 872504257}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &872504259 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 1077107949267788258, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
  m_PrefabInstance: {fileID: 872504257}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 67db9e8f0e2ae9c40bc1e2b64352a6b4, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!224 &883955027 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
  m_PrefabInstance: {fileID: 1731273250}
  m_PrefabAsset: {fileID: 0}
--- !u!223 &883955029 stripped
Canvas:
  m_CorrespondingSourceObject: {fileID: 3022974012137700811, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
  m_PrefabInstance: {fileID: 1731273250}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &895525033
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 895525034}
  - component: {fileID: 895525038}
  - component: {fileID: 895525037}
  - component: {fileID: 895525036}
  - component: {fileID: 895525039}
  m_Layer: 5
  m_Name: MeterAqua (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &895525034
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 895525033}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 510766279}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.2, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: -10, y: 80}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &895525036
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 895525033}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a0fdf6750a7499e4faef09a8cc8ddfde, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &895525037
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 895525033}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 2100000, guid: a16742ff981b9814d96283c1cff28880, type: 2}
  m_Color: {r: 0.0141509175, g: 0.5704708, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &895525038
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 895525033}
  m_CullTransparentMesh: 1
--- !u!114 &895525039
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 895525033}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 33236b32a07827145b40a42b9033672e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _value: {x: 0, y: 1}
  _direction: 0
  defaultMaterial: {fileID: -876546973899608171, guid: 671c5a7d7c5e6bc40bbae6edd1955c32, type: 3}
--- !u!1 &913561107
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 913561108}
  - component: {fileID: 913561111}
  - component: {fileID: 913561110}
  - component: {fileID: 913561109}
  - component: {fileID: 913561112}
  m_Layer: 5
  m_Name: Lower Shelf
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &913561108
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 913561107}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2001928625}
  - {fileID: 1231661143}
  - {fileID: 392127263}
  - {fileID: 1840198704}
  m_Father: {fileID: 1005163284}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &913561109
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 913561107}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a0fdf6750a7499e4faef09a8cc8ddfde, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &913561110
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 913561107}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 2100000, guid: f0eb4d347c180c14ca49bca838efbe8a, type: 2}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &913561111
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 913561107}
  m_CullTransparentMesh: 1
--- !u!114 &913561112
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 913561107}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 30649d3a9faa99c48a7b1166b86bf2a0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Padding:
    m_Left: 100
    m_Right: 100
    m_Top: -100
    m_Bottom: -100
  m_ChildAlignment: 4
  m_Spacing: 0
  m_ChildForceExpandWidth: 1
  m_ChildForceExpandHeight: 1
  m_ChildControlWidth: 0
  m_ChildControlHeight: 0
  m_ChildScaleWidth: 0
  m_ChildScaleHeight: 0
  m_ReverseArrangement: 0
--- !u!1 &916168808
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 916168809}
  - component: {fileID: 916168812}
  - component: {fileID: 916168811}
  - component: {fileID: 916168810}
  m_Layer: 5
  m_Name: ButtonSimple (3)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &916168809
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 916168808}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1501147054}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 1, y: 0.5}
  m_AnchoredPosition: {x: -47.5, y: 0}
  m_SizeDelta: {x: -105, y: 80}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &916168810
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 916168808}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a0fdf6750a7499e4faef09a8cc8ddfde, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &916168811
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 916168808}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 2100000, guid: 852cbe2a621ecc64a96412d473138ac8, type: 2}
  m_Color: {r: 0.3443396, g: 1, b: 0.40522814, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &916168812
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 916168808}
  m_CullTransparentMesh: 1
--- !u!224 &927756591 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 4262581316619277117, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
  m_PrefabInstance: {fileID: 1731273250}
  m_PrefabAsset: {fileID: 0}
--- !u!224 &1005163284 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 4262581316619277117, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
  m_PrefabInstance: {fileID: 402657851}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &1005163286
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 487747952}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 59f8146938fff824cb5fd77236b75775, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Padding:
    m_Left: 0
    m_Right: 0
    m_Top: 0
    m_Bottom: 10
  m_ChildAlignment: 0
  m_Spacing: 10
  m_ChildForceExpandWidth: 1
  m_ChildForceExpandHeight: 1
  m_ChildControlWidth: 1
  m_ChildControlHeight: 1
  m_ChildScaleWidth: 0
  m_ChildScaleHeight: 0
  m_ReverseArrangement: 0
--- !u!1 &1035058065
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1035058066}
  - component: {fileID: 1035058069}
  - component: {fileID: 1035058068}
  m_Layer: 5
  m_Name: SimpleLoading
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1035058066
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1035058065}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1900708143}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 180, y: 180}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1035058068
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1035058065}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: -876546973899608171, guid: da1e7c82159167748a28f5478add66ea, type: 3}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1035058069
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1035058065}
  m_CullTransparentMesh: 1
--- !u!1001 &1043085071
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 1395715298}
    m_Modifications:
    - target: {fileID: 2111605945800068879, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_SizeDelta.x
      value: -200
      objectReference: {fileID: 0}
    - target: {fileID: 2111605945800068879, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_SizeDelta.y
      value: -90
      objectReference: {fileID: 0}
    - target: {fileID: 2111605945800068879, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2111605945800068879, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -35
      objectReference: {fileID: 0}
    - target: {fileID: 3022974012137700811, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_Enabled
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 3022974012137700811, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_VertexColorAlwaysGammaSpace
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 3022974012137700811, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_AdditionalShaderChannelsFlag
      value: 25
      objectReference: {fileID: 0}
    - target: {fileID: 3097647832025268333, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_text
      value: "Welcome to the Shader Graph UGUI Shaders sample content. This set of
        samples contains over a dozen user interface widgets, buttons, and backgrounds
        - all generated procedurally using Shader Graph.  It also contains a library
        of more than 50 UI-specific subgraphs that you can use to build user interface
        elements.  The sample shaders work in both HDRP and URP.\n\nWe have two main
        objectives with this sample set:\r\n\r\n1. Demonstrate Shader Graph\u2019s
        ability to create dynamic, resolution-independent user interface elements
        in a wide variety of shapes and styles.\n\r\n2. Make it easier and faster
        to create UI elements by providing a large set of UI-specific subgraph nodes
        that can be used as building blocks to speed up the creation process.\n\nAs
        these samples demonstrate, using Shader Graph and UGUI, you can create user
        interface elements that are resolution independent, require zero texture
        memory, can be authored and edited directly in Shader Graph inside of Unity,
        automatically adapt to aspect ratio, and contain all visual states and behaviors.\r\n\nTo
        get started exploring the content, enter Play mode and then click on one
        of the tabs to the left.\n\nNote: If you are using the New Input System,
        you need to select the EventSystem GameObject and click on \"Replace with
        InputSystemUIInputModule.\""
      objectReference: {fileID: 0}
    - target: {fileID: 4262581316619277117, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4262581316619277117, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5597060975141118542, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7513160448566667922, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_text
      value: Shader Graph UGUI Shaders Sample
      objectReference: {fileID: 0}
    - target: {fileID: 7513160448566667922, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_fontSize
      value: 54
      objectReference: {fileID: 0}
    - target: {fileID: 7513160448566667922, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_fontStyle
      value: 17
      objectReference: {fileID: 0}
    - target: {fileID: 7513160448566667922, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_fontColor.b
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7513160448566667922, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_fontColor.g
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7513160448566667922, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_fontColor.r
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7513160448566667922, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_fontSizeBase
      value: 54
      objectReference: {fileID: 0}
    - target: {fileID: 7513160448566667922, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_fontColor32.rgba
      value: 4278190080
      objectReference: {fileID: 0}
    - target: {fileID: 7661986276067569678, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7661986276067569678, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_SizeDelta.y
      value: -120
      objectReference: {fileID: 0}
    - target: {fileID: 8208681277822599831, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_Name
      value: Page - Welcome
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_Pivot.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_Pivot.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_AnchorMax.x
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_SizeDelta.x
      value: -420
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_SizeDelta.y
      value: -40
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 190
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8405060111705126841, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_text
      value: 'Enter Play Mode to interact with the UI.


        The tabs on the
        left use Custom Toggles to set their material properties such as the Selectable
        State (Normal, Highlighted, Pressed, Selected, and Disabled) and Toggle isOn
        state.'
      objectReference: {fileID: 0}
    - target: {fileID: 8405060111705126841, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_fontSize
      value: 30
      objectReference: {fileID: 0}
    - target: {fileID: 8405060111705126841, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_fontSizeBase
      value: 30
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
--- !u!224 &1043085072 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
  m_PrefabInstance: {fileID: 1043085071}
  m_PrefabAsset: {fileID: 0}
--- !u!223 &1043085074 stripped
Canvas:
  m_CorrespondingSourceObject: {fileID: 3022974012137700811, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
  m_PrefabInstance: {fileID: 1043085071}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1069439669
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1069439670}
  - component: {fileID: 1069439672}
  - component: {fileID: 1069439671}
  m_Layer: 5
  m_Name: Background
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1069439670
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1069439669}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 666825513}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0.25}
  m_AnchorMax: {x: 1, y: 0.75}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1069439671
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1069439669}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10907, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1069439672
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1069439669}
  m_CullTransparentMesh: 1
--- !u!1 &1084581134
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1084581137}
  - component: {fileID: 1084581136}
  - component: {fileID: 1084581135}
  m_Layer: 0
  m_Name: Main Camera
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!81 &1084581135
AudioListener:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1084581134}
  m_Enabled: 1
--- !u!20 &1084581136
Camera:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1084581134}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 2
  m_BackGroundColor: {r: 0.16078432, g: 0.16078432, b: 0.16078432, a: 0}
  m_projectionMatrixMode: 1
  m_GateFitMode: 2
  m_FOVAxisMode: 0
  m_Iso: 200
  m_ShutterSpeed: 0.005
  m_Aperture: 16
  m_FocusDistance: 10
  m_FocalLength: 50
  m_BladeCount: 5
  m_Curvature: {x: 2, y: 11}
  m_BarrelClipping: 0.25
  m_Anamorphism: 0
  m_SensorSize: {x: 36, y: 24}
  m_LensShift: {x: 0, y: 0}
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: 0.3
  far clip plane: 1000
  field of view: 60
  orthographic: 0
  orthographic size: 5
  m_Depth: -1
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_TargetDisplay: 0
  m_TargetEye: 3
  m_HDR: 0
  m_AllowMSAA: 0
  m_AllowDynamicResolution: 0
  m_ForceIntoRT: 0
  m_OcclusionCulling: 1
  m_StereoConvergence: 10
  m_StereoSeparation: 0.022
--- !u!4 &1084581137
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1084581134}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 1, z: -10}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1084802477
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1084802480}
  - component: {fileID: 1084802479}
  - component: {fileID: 1084802481}
  m_Layer: 0
  m_Name: EventSystem
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1084802479
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1084802477}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 76c392e42b5098c458856cdf6ecaaaa1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_FirstSelected: {fileID: 0}
  m_sendNavigationEvents: 1
  m_DragThreshold: 10
--- !u!4 &1084802480
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1084802477}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1084802481
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1084802477}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4f231c4fb786f3946a6b90b886c48677, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_SendPointerHoverToParent: 1
  m_HorizontalAxis: Horizontal
  m_VerticalAxis: Vertical
  m_SubmitButton: Submit
  m_CancelButton: Cancel
  m_InputActionsPerSecond: 10
  m_RepeatDelay: 0.5
  m_ForceModuleActive: 0
--- !u!1 &1096159063
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1096159064}
  - component: {fileID: 1096159067}
  - component: {fileID: 1096159066}
  - component: {fileID: 1096159068}
  m_Layer: 5
  m_Name: ProgressCircle
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1096159064
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1096159063}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: **********}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 200, y: 200}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1096159066
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1096159063}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: -876546973899608171, guid: 1287bd36238408e4586f632ee6bbdfe0, type: 3}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1096159067
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1096159063}
  m_CullTransparentMesh: 1
--- !u!114 &1096159068
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1096159063}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ea0e4892b6614c84bb5a0598a1e20167, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _value: 0.519
  defaultMaterial: {fileID: -876546973899608171, guid: 671c5a7d7c5e6bc40bbae6edd1955c32, type: 3}
--- !u!1 &1134473702
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1134473703}
  - component: {fileID: 1134473706}
  - component: {fileID: 1134473705}
  - component: {fileID: 1134473707}
  m_Layer: 5
  m_Name: MeterFantasy (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1134473703
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1134473702}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: **********}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 250, y: 250}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1134473705
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1134473702}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 2100000, guid: 690a807caf68b654ba39a9f991f81975, type: 2}
  m_Color: {r: 1, g: 0.11694199, b: 0, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1134473706
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1134473702}
  m_CullTransparentMesh: 1
--- !u!114 &1134473707
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1134473702}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ea0e4892b6614c84bb5a0598a1e20167, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _value: 0.519
  defaultMaterial: {fileID: -876546973899608171, guid: 671c5a7d7c5e6bc40bbae6edd1955c32, type: 3}
--- !u!1 &1198096935
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1198096936}
  - component: {fileID: 1198096939}
  - component: {fileID: 1198096938}
  - component: {fileID: 1198096937}
  m_Layer: 5
  m_Name: ButtonSimple (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1198096936
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1198096935}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1501147054}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.2, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: -10, y: 80}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1198096937
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1198096935}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a0fdf6750a7499e4faef09a8cc8ddfde, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &1198096938
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1198096935}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 2100000, guid: 852cbe2a621ecc64a96412d473138ac8, type: 2}
  m_Color: {r: 0.3443396, g: 1, b: 0.40522814, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1198096939
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1198096935}
  m_CullTransparentMesh: 1
--- !u!1 &1231661142
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1231661143}
  - component: {fileID: 1231661149}
  - component: {fileID: 1231661148}
  - component: {fileID: 1231661150}
  - component: {fileID: 1231661151}
  m_Layer: 5
  m_Name: ButtonAqua
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1231661143
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1231661142}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 142244314}
  m_Father: {fileID: 913561108}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 300, y: 100}
  m_Pivot: {x: 1, y: 0.5}
--- !u!114 &1231661148
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1231661142}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 2100000, guid: 75fe72b1a99c32848a4de1d541cd14da, type: 2}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1231661149
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1231661142}
  m_CullTransparentMesh: 1
--- !u!114 &1231661150
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1231661142}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 457b592a913195e4b8cb0804abbe40de, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 0
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 1231661148}
  onClick:
    m_PersistentCalls:
      m_Calls: []
  onStateChanged:
    m_PersistentCalls:
      m_Calls: []
  defaultMaterial: {fileID: -876546973899608171, guid: 9166601a958188e4caa2b1418d72baaf, type: 3}
--- !u!114 &1231661151
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1231661142}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a0fdf6750a7499e4faef09a8cc8ddfde, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &1350527057
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1350527058}
  - component: {fileID: 1350527061}
  - component: {fileID: 1350527060}
  - component: {fileID: 1350527059}
  - component: {fileID: 1350527063}
  m_Layer: 5
  m_Name: MeterSciFi
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1350527058
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1350527057}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1999474827}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 1}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: -60, y: -40}
  m_SizeDelta: {x: -60, y: 100}
  m_Pivot: {x: 1, y: 1}
--- !u!114 &1350527059
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1350527057}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a0fdf6750a7499e4faef09a8cc8ddfde, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &1350527060
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1350527057}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 2100000, guid: 389ca7f0ad59e0648946a7dbcce24bd5, type: 2}
  m_Color: {r: 0.52400005, g: 0.8245333, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1350527061
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1350527057}
  m_CullTransparentMesh: 1
--- !u!114 &1350527063
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1350527057}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fb201a23cacc2a7479c31ac9c37407c5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 0
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 1350527060}
  _direction: 0
  _value: 0.5
  onStateChanged:
    m_PersistentCalls:
      m_Calls: []
  onValueChanged:
    m_PersistentCalls:
      m_Calls: []
  defaultMaterial: {fileID: 0}
--- !u!1 &1395715294
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1395715298}
  - component: {fileID: 1395715297}
  - component: {fileID: 1395715296}
  - component: {fileID: 1395715295}
  m_Layer: 5
  m_Name: Canvas
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1395715295
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1395715294}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc42784cf147c0c48a680349fa168899, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreReversedGraphics: 1
  m_BlockingObjects: 0
  m_BlockingMask:
    serializedVersion: 2
    m_Bits: 4294967295
--- !u!114 &1395715296
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1395715294}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0cd44c1031e13a943bb63640046fad76, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UiScaleMode: 1
  m_ReferencePixelsPerUnit: 100
  m_ScaleFactor: 1
  m_ReferenceResolution: {x: 1920, y: 1080}
  m_ScreenMatchMode: 0
  m_MatchWidthOrHeight: 0
  m_PhysicalUnit: 3
  m_FallbackScreenDPI: 96
  m_DefaultSpriteDPI: 96
  m_DynamicPixelsPerUnit: 1
  m_PresetInfoIsWorld: 0
--- !u!223 &1395715297
Canvas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1395715294}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 0
  m_Camera: {fileID: 0}
  m_PlaneDistance: 100
  m_PixelPerfect: 0
  m_ReceivesEvents: 1
  m_OverrideSorting: 0
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_VertexColorAlwaysGammaSpace: 1
  m_AdditionalShaderChannelsFlag: 25
  m_UpdateRectTransformForStandalone: 0
  m_SortingLayerID: 0
  m_SortingOrder: 0
  m_TargetDisplay: 0
--- !u!224 &1395715298
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1395715294}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0, y: 0, z: 0}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1826442488}
  - {fileID: 531894451}
  - {fileID: 1043085072}
  - {fileID: 883955027}
  - {fileID: 471393352}
  - {fileID: 1404563809}
  - {fileID: 402657852}
  - {fileID: 2143050078}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0, y: 0}
--- !u!1 &1402949187
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1402949191}
  - component: {fileID: 1402949190}
  - component: {fileID: 1402949189}
  - component: {fileID: 1402949188}
  m_Layer: 0
  m_Name: BackgroundSphere
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!135 &1402949188
SphereCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1402949187}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Radius: 0.5
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &1402949189
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1402949187}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: -876546973899608171, guid: f09c92b1410993b49905380a871a72c9, type: 3}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1402949190
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1402949187}
  m_Mesh: {fileID: 10207, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &1402949191
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1402949187}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 25, y: 25, z: 25}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &1404563808
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 1395715298}
    m_Modifications:
    - target: {fileID: 3022974012137700811, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_Enabled
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3022974012137700811, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_AdditionalShaderChannelsFlag
      value: 25
      objectReference: {fileID: 0}
    - target: {fileID: 3097647832025268333, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_text
      value: These Meter elements communicate information to the player - such as
        health, magic, or shield level. You can adjust the slider above to see how
        they work.
      objectReference: {fileID: 0}
    - target: {fileID: 7513160448566667922, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_text
      value: Meters
      objectReference: {fileID: 0}
    - target: {fileID: 7513160448566667922, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_fontSize
      value: 54
      objectReference: {fileID: 0}
    - target: {fileID: 7513160448566667922, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_fontStyle
      value: 17
      objectReference: {fileID: 0}
    - target: {fileID: 7513160448566667922, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_fontSizeBase
      value: 54
      objectReference: {fileID: 0}
    - target: {fileID: 8208681277822599831, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_Name
      value: Page - Meters
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_Pivot.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_Pivot.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_AnchorMax.x
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_SizeDelta.x
      value: -420
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_SizeDelta.y
      value: -40
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 190
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8405060111705126841, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_text
      value: Meters show the player a specific piece of information - like health,
        power, ammo, sheild strength, etc. You can control the indicated levels on
        these meters by sliding the slider at the top of the interface.
      objectReference: {fileID: 0}
    - target: {fileID: 8405060111705126841, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_fontSize
      value: 30
      objectReference: {fileID: 0}
    - target: {fileID: 8405060111705126841, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_fontSizeBase
      value: 30
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects:
    - targetCorrespondingSourceObject: {fileID: 4262581316619277117, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      insertIndex: -1
      addedObject: {fileID: **********}
    - targetCorrespondingSourceObject: {fileID: 4262581316619277117, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      insertIndex: -1
      addedObject: {fileID: **********}
    - targetCorrespondingSourceObject: {fileID: 4262581316619277117, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      insertIndex: -1
      addedObject: {fileID: 666825513}
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: 5597060975141118542, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      insertIndex: -1
      addedObject: {fileID: 1404563813}
  m_SourcePrefab: {fileID: 100100000, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
--- !u!224 &1404563809 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
  m_PrefabInstance: {fileID: 1404563808}
  m_PrefabAsset: {fileID: 0}
--- !u!224 &1404563810 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 4262581316619277117, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
  m_PrefabInstance: {fileID: 1404563808}
  m_PrefabAsset: {fileID: 0}
--- !u!223 &1404563811 stripped
Canvas:
  m_CorrespondingSourceObject: {fileID: 3022974012137700811, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
  m_PrefabInstance: {fileID: 1404563808}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &1404563813
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 362484447}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 30649d3a9faa99c48a7b1166b86bf2a0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Padding:
    m_Left: 0
    m_Right: 0
    m_Top: 0
    m_Bottom: 80
  m_ChildAlignment: 0
  m_Spacing: 10
  m_ChildForceExpandWidth: 1
  m_ChildForceExpandHeight: 1
  m_ChildControlWidth: 1
  m_ChildControlHeight: 1
  m_ChildScaleWidth: 0
  m_ChildScaleHeight: 0
  m_ReverseArrangement: 0
--- !u!1 &1408832657
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1408832658}
  - component: {fileID: 1408832661}
  - component: {fileID: 1408832660}
  - component: {fileID: 1408832662}
  - component: {fileID: 1408832663}
  m_Layer: 5
  m_Name: GradientBar
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1408832658
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1408832657}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1999474827}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 0}
  m_AnchoredPosition: {x: 0, y: 40}
  m_SizeDelta: {x: -80, y: 100}
  m_Pivot: {x: 0.5, y: 0}
--- !u!114 &1408832660
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1408832657}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 2100000, guid: 507e302cbcc7faf4fa607d79de6151e3, type: 2}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1408832661
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1408832657}
  m_CullTransparentMesh: 1
--- !u!114 &1408832662
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1408832657}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a0fdf6750a7499e4faef09a8cc8ddfde, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &1408832663
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1408832657}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fb201a23cacc2a7479c31ac9c37407c5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 0
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 1408832660}
  _direction: 0
  _value: 0.5
  onStateChanged:
    m_PersistentCalls:
      m_Calls: []
  onValueChanged:
    m_PersistentCalls:
      m_Calls: []
  defaultMaterial: {fileID: 0}
--- !u!1001 &1430991128
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 1649024369}
    m_Modifications:
    - target: {fileID: 92038933656319273, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_Name
      value: Hexagon Dissolve Slider
      objectReference: {fileID: 0}
    - target: {fileID: 905015512013034897, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_text
      value: Hexagon Dissolve
      objectReference: {fileID: 0}
    - target: {fileID: 905015512013034897, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_fontStyle
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 905015512013034897, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_fontColor.b
      value: 0.72955966
      objectReference: {fileID: 0}
    - target: {fileID: 905015512013034897, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_fontColor.g
      value: 0.72955966
      objectReference: {fileID: 0}
    - target: {fileID: 905015512013034897, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_fontColor.r
      value: 0.72955966
      objectReference: {fileID: 0}
    - target: {fileID: 905015512013034897, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_fontColor32.rgba
      value: 4290427578
      objectReference: {fileID: 0}
    - target: {fileID: 6753850657890399353, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_Name
      value: Hexagon Dissolve Slider
      objectReference: {fileID: 0}
    - target: {fileID: 7194105814776850979, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7194105814776850979, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_Pivot.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_Pivot.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_AnchorMax.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8561477015106430877, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8561477015106430877, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
--- !u!224 &1430991129 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
  m_PrefabInstance: {fileID: 1430991128}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &1430991130 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 1077107949267788258, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
  m_PrefabInstance: {fileID: 1430991128}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 67db9e8f0e2ae9c40bc1e2b64352a6b4, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &1501147053
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1501147054}
  - component: {fileID: 1501147057}
  - component: {fileID: 1501147056}
  - component: {fileID: 1501147055}
  m_Layer: 5
  m_Name: Lower Shelf
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1501147054
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1501147053}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 6}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 380862420}
  - {fileID: 1198096936}
  - {fileID: 916168809}
  m_Father: {fileID: 2097437749}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1501147055
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1501147053}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a0fdf6750a7499e4faef09a8cc8ddfde, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &1501147056
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1501147053}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 2100000, guid: f0eb4d347c180c14ca49bca838efbe8a, type: 2}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1501147057
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1501147053}
  m_CullTransparentMesh: 1
--- !u!1001 &1549657828
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 1649024369}
    m_Modifications:
    - target: {fileID: 92038933656319273, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_Name
      value: Tint Red Slider
      objectReference: {fileID: 0}
    - target: {fileID: 905015512013034897, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_text
      value: Tint / Red
      objectReference: {fileID: 0}
    - target: {fileID: 905015512013034897, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_fontStyle
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 905015512013034897, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_fontColor.b
      value: 0.72955966
      objectReference: {fileID: 0}
    - target: {fileID: 905015512013034897, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_fontColor.g
      value: 0.72955966
      objectReference: {fileID: 0}
    - target: {fileID: 905015512013034897, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_fontColor.r
      value: 0.72955966
      objectReference: {fileID: 0}
    - target: {fileID: 905015512013034897, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_fontColor32.rgba
      value: 4290427578
      objectReference: {fileID: 0}
    - target: {fileID: 6753850657890399353, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_Name
      value: Tint Red Slider
      objectReference: {fileID: 0}
    - target: {fileID: 7194105814776850979, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7194105814776850979, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_Pivot.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_Pivot.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_AnchorMax.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8561477015106430877, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8561477015106430877, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
--- !u!224 &1549657829 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
  m_PrefabInstance: {fileID: 1549657828}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &1549657830 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 1077107949267788258, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
  m_PrefabInstance: {fileID: 1549657828}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 67db9e8f0e2ae9c40bc1e2b64352a6b4, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &1642025956
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1642025957}
  - component: {fileID: 1642025959}
  - component: {fileID: 1642025958}
  m_Layer: 5
  m_Name: Handle
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1642025957
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1642025956}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1848559309}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 20, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1642025958
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1642025956}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10913, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1642025959
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1642025956}
  m_CullTransparentMesh: 1
--- !u!1 &1649024368
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1649024369}
  - component: {fileID: 1649024373}
  - component: {fileID: 1649024372}
  - component: {fileID: 1649024371}
  - component: {fileID: 1649024370}
  m_Layer: 5
  m_Name: Background Control Panel
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1649024369
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1649024368}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2236064079864338392}
  - {fileID: 1549657829}
  - {fileID: 872504258}
  - {fileID: 73290143}
  - {fileID: 1430991129}
  - {fileID: 114155307}
  - {fileID: 654503346}
  m_Father: {fileID: 927756591}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 1, y: 1}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 625.5, y: 0}
  m_Pivot: {x: 1, y: 1}
--- !u!114 &1649024370
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1649024368}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3245ec927659c4140ac4f8d17403cc18, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_HorizontalFit: 0
  m_VerticalFit: 2
--- !u!114 &1649024371
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1649024368}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 59f8146938fff824cb5fd77236b75775, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Padding:
    m_Left: 20
    m_Right: 20
    m_Top: 20
    m_Bottom: 20
  m_ChildAlignment: 0
  m_Spacing: 10
  m_ChildForceExpandWidth: 1
  m_ChildForceExpandHeight: 1
  m_ChildControlWidth: 1
  m_ChildControlHeight: 0
  m_ChildScaleWidth: 0
  m_ChildScaleHeight: 0
  m_ReverseArrangement: 0
--- !u!114 &1649024372
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1649024368}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0, g: 0, b: 0, a: 0.87058824}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10907, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 0.5
--- !u!222 &1649024373
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1649024368}
  m_CullTransparentMesh: 1
--- !u!1001 &1731273250
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 1395715298}
    m_Modifications:
    - target: {fileID: 3022974012137700811, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_Enabled
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3022974012137700811, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_AdditionalShaderChannelsFlag
      value: 25
      objectReference: {fileID: 0}
    - target: {fileID: 3097647832025268333, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_text
      value: 'We''ll start with the background. This shader blurs the scene behind
        it so you can focus attention on the UI while still maintaining a presence
        in the scene.

        You can change the background shader''s parameters
        by adjusting the sliders above.'
      objectReference: {fileID: 0}
    - target: {fileID: 5597060975141118542, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7513160448566667922, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_text
      value: Background Processing
      objectReference: {fileID: 0}
    - target: {fileID: 7513160448566667922, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_fontSize
      value: 54
      objectReference: {fileID: 0}
    - target: {fileID: 7513160448566667922, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_fontStyle
      value: 17
      objectReference: {fileID: 0}
    - target: {fileID: 7513160448566667922, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_fontSizeBase
      value: 54
      objectReference: {fileID: 0}
    - target: {fileID: 8208681277822599831, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_Name
      value: Page - Background
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_Pivot.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_Pivot.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_AnchorMax.x
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_SizeDelta.x
      value: -420
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_SizeDelta.y
      value: -40
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 190
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8405060111705126841, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_text
      value: 'UI Shaders can process the background, offering a wide range of effects
        from simple desaturation to advanced blur.

        The sliders above control
        the background material parameters.'
      objectReference: {fileID: 0}
    - target: {fileID: 8405060111705126841, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_fontSize
      value: 30
      objectReference: {fileID: 0}
    - target: {fileID: 8405060111705126841, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_fontSizeBase
      value: 30
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects:
    - targetCorrespondingSourceObject: {fileID: 4262581316619277117, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      insertIndex: -1
      addedObject: {fileID: 1649024369}
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
--- !u!1 &1826442487
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1826442488}
  - component: {fileID: 1826442490}
  - component: {fileID: 1826442489}
  - component: {fileID: 1826442491}
  m_Layer: 5
  m_Name: Background
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1826442488
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1826442487}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1395715298}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: -40, y: -40}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1826442489
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1826442487}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 2100000, guid: 412b0545276a4ba46bdc8b43d16a95ac, type: 2}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 0
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1826442490
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1826442487}
  m_CullTransparentMesh: 1
--- !u!114 &1826442491
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1826442487}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9f19c97b36748f849a73a3321b7e3c64, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _saturationSlider: {fileID: 199872146}
  _tintRedSlider: {fileID: 1549657830}
  _tintGreenSlider: {fileID: 872504259}
  _tintBlueSlider: {fileID: 73290144}
  _hexagonDissolveSlider: {fileID: 1430991130}
  _hexagonTilesSlider: {fileID: 114155308}
  _blurSlider: {fileID: 654503347}
  _blurCyclesSlider: {fileID: 0}
  _blurSamplesPerCycleSlider: {fileID: 0}
--- !u!1 &1840198703
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1840198704}
  - component: {fileID: 1840198710}
  - component: {fileID: 1840198709}
  - component: {fileID: 1840198711}
  - component: {fileID: 1840198712}
  m_Layer: 5
  m_Name: ButtonSimple
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1840198704
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1840198703}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 382653530}
  m_Father: {fileID: 913561108}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 300, y: 100}
  m_Pivot: {x: 1, y: 0.5}
--- !u!114 &1840198709
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1840198703}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 2100000, guid: 852cbe2a621ecc64a96412d473138ac8, type: 2}
  m_Color: {r: 0.3443396, g: 1, b: 0.40522814, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1840198710
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1840198703}
  m_CullTransparentMesh: 1
--- !u!114 &1840198711
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1840198703}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 457b592a913195e4b8cb0804abbe40de, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 0
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 1840198709}
  onClick:
    m_PersistentCalls:
      m_Calls: []
  onStateChanged:
    m_PersistentCalls:
      m_Calls: []
  defaultMaterial: {fileID: -876546973899608171, guid: 9166601a958188e4caa2b1418d72baaf, type: 3}
--- !u!114 &1840198712
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1840198703}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a0fdf6750a7499e4faef09a8cc8ddfde, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &1848559308
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1848559309}
  m_Layer: 5
  m_Name: Handle Slide Area
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1848559309
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1848559308}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1642025957}
  m_Father: {fileID: 666825513}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: -20, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &1900708142
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1900708143}
  - component: {fileID: 1900708147}
  - component: {fileID: 1900708146}
  - component: {fileID: 1900708145}
  - component: {fileID: 1900708144}
  m_Layer: 5
  m_Name: Lower Shelf
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1900708143
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1900708142}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1035058066}
  - {fileID: 153029047}
  m_Father: {fileID: 2143050079}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0.5}
  m_AnchorMax: {x: 1, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 500}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1900708144
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1900708142}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 30649d3a9faa99c48a7b1166b86bf2a0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Padding:
    m_Left: 10
    m_Right: 10
    m_Top: 10
    m_Bottom: 10
  m_ChildAlignment: 4
  m_Spacing: 20
  m_ChildForceExpandWidth: 1
  m_ChildForceExpandHeight: 1
  m_ChildControlWidth: 0
  m_ChildControlHeight: 0
  m_ChildScaleWidth: 0
  m_ChildScaleHeight: 0
  m_ReverseArrangement: 0
--- !u!114 &1900708145
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1900708142}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a0fdf6750a7499e4faef09a8cc8ddfde, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &1900708146
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1900708142}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 2100000, guid: f0eb4d347c180c14ca49bca838efbe8a, type: 2}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1900708147
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1900708142}
  m_CullTransparentMesh: 1
--- !u!1 &1924907263
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1924907264}
  - component: {fileID: 1924907267}
  - component: {fileID: 1924907266}
  - component: {fileID: 1924907268}
  m_Layer: 5
  m_Name: MeterDial
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1924907264
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1924907263}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: **********}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 200, y: 200}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1924907266
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1924907263}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 2100000, guid: 94abe21540d2ede42aba0a185864af88, type: 2}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1924907267
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1924907263}
  m_CullTransparentMesh: 1
--- !u!114 &1924907268
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1924907263}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ea0e4892b6614c84bb5a0598a1e20167, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _value: 0.519
  defaultMaterial: {fileID: -876546973899608171, guid: 671c5a7d7c5e6bc40bbae6edd1955c32, type: 3}
--- !u!1 &1972633161
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1972633162}
  - component: {fileID: 1972633165}
  - component: {fileID: 1972633164}
  - component: {fileID: 1972633166}
  m_Layer: 5
  m_Name: MeterFantasy
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1972633162
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1972633161}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: **********}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 250, y: 250}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1972633164
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1972633161}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 2100000, guid: 690a807caf68b654ba39a9f991f81975, type: 2}
  m_Color: {r: 0.80276585, g: 0, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1972633165
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1972633161}
  m_CullTransparentMesh: 1
--- !u!114 &1972633166
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1972633161}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ea0e4892b6614c84bb5a0598a1e20167, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _value: 0.519
  defaultMaterial: {fileID: -876546973899608171, guid: 671c5a7d7c5e6bc40bbae6edd1955c32, type: 3}
--- !u!1 &1982639598
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1982639599}
  - component: {fileID: 1982639603}
  - component: {fileID: 1982639602}
  - component: {fileID: 1982639601}
  - component: {fileID: 1982639604}
  m_Layer: 5
  m_Name: MeterAqua (3)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1982639599
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1982639598}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 510766279}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 1, y: 0.5}
  m_AnchoredPosition: {x: -47.5, y: 0}
  m_SizeDelta: {x: -105, y: 80}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1982639601
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1982639598}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a0fdf6750a7499e4faef09a8cc8ddfde, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &1982639602
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1982639598}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 2100000, guid: a16742ff981b9814d96283c1cff28880, type: 2}
  m_Color: {r: 0.0141509175, g: 0.5704708, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1982639603
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1982639598}
  m_CullTransparentMesh: 1
--- !u!114 &1982639604
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1982639598}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 33236b32a07827145b40a42b9033672e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _value: {x: 0, y: 1}
  _direction: 0
  defaultMaterial: {fileID: -876546973899608171, guid: 671c5a7d7c5e6bc40bbae6edd1955c32, type: 3}
--- !u!1 &1999474826
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1999474827}
  - component: {fileID: 1999474830}
  - component: {fileID: 1999474829}
  - component: {fileID: 1999474828}
  m_Layer: 5
  m_Name: Upper Shelf
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1999474827
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1999474826}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 721419997}
  - {fileID: 1350527058}
  - {fileID: 1408832658}
  m_Father: {fileID: 1005163284}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1999474828
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1999474826}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a0fdf6750a7499e4faef09a8cc8ddfde, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &1999474829
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1999474826}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 2100000, guid: f0eb4d347c180c14ca49bca838efbe8a, type: 2}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1999474830
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1999474826}
  m_CullTransparentMesh: 1
--- !u!1 &2001928624
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2001928625}
  - component: {fileID: 2001928631}
  - component: {fileID: 2001928630}
  - component: {fileID: 2001928632}
  m_Layer: 5
  m_Name: ButtonSciFi
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &2001928625
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2001928624}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 913561108}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 200, y: 200}
  m_Pivot: {x: 0, y: 0.5}
--- !u!114 &2001928630
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2001928624}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 2100000, guid: 9bac1c6c3e545804aa84b4cd5e5adbb8, type: 2}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &2001928631
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2001928624}
  m_CullTransparentMesh: 1
--- !u!114 &2001928632
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2001928624}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 457b592a913195e4b8cb0804abbe40de, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 0
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 2001928630}
  onClick:
    m_PersistentCalls:
      m_Calls: []
  onStateChanged:
    m_PersistentCalls:
      m_Calls: []
  defaultMaterial: {fileID: -876546973899608171, guid: 9166601a958188e4caa2b1418d72baaf, type: 3}
--- !u!1 &2023254962
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: **********}
  - component: {fileID: 2023254967}
  - component: {fileID: 2023254966}
  - component: {fileID: 2023254965}
  - component: {fileID: 2023254964}
  m_Layer: 5
  m_Name: Right Container
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &**********
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2023254962}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1972633162}
  - {fileID: 1134473703}
  m_Father: {fileID: 1404563810}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &2023254964
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2023254962}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 59f8146938fff824cb5fd77236b75775, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Padding:
    m_Left: 0
    m_Right: 0
    m_Top: 30
    m_Bottom: 30
  m_ChildAlignment: 4
  m_Spacing: 0
  m_ChildForceExpandWidth: 1
  m_ChildForceExpandHeight: 1
  m_ChildControlWidth: 0
  m_ChildControlHeight: 0
  m_ChildScaleWidth: 0
  m_ChildScaleHeight: 0
  m_ReverseArrangement: 0
--- !u!114 &2023254965
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2023254962}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a0fdf6750a7499e4faef09a8cc8ddfde, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &2023254966
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2023254962}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 2100000, guid: f0eb4d347c180c14ca49bca838efbe8a, type: 2}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &2023254967
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2023254962}
  m_CullTransparentMesh: 1
--- !u!1 &2050667439
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: **********}
  - component: {fileID: 2050667444}
  - component: {fileID: 2050667443}
  - component: {fileID: 2050667442}
  - component: {fileID: 2050667441}
  m_Layer: 5
  m_Name: Left Container
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &**********
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2050667439}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1096159064}
  - {fileID: 1924907264}
  m_Father: {fileID: 1404563810}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &2050667441
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2050667439}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 59f8146938fff824cb5fd77236b75775, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Padding:
    m_Left: 0
    m_Right: 0
    m_Top: 60
    m_Bottom: 60
  m_ChildAlignment: 4
  m_Spacing: 0
  m_ChildForceExpandWidth: 1
  m_ChildForceExpandHeight: 1
  m_ChildControlWidth: 0
  m_ChildControlHeight: 0
  m_ChildScaleWidth: 0
  m_ChildScaleHeight: 0
  m_ReverseArrangement: 0
--- !u!114 &2050667442
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2050667439}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a0fdf6750a7499e4faef09a8cc8ddfde, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &2050667443
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2050667439}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 2100000, guid: f0eb4d347c180c14ca49bca838efbe8a, type: 2}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &2050667444
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2050667439}
  m_CullTransparentMesh: 1
--- !u!1 &2097437748 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 5597060975141118542, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
  m_PrefabInstance: {fileID: 1112707155501840389}
  m_PrefabAsset: {fileID: 0}
--- !u!224 &2097437749 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 4262581316619277117, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
  m_PrefabInstance: {fileID: 1112707155501840389}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &2097437752
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2097437748}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 59f8146938fff824cb5fd77236b75775, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Padding:
    m_Left: 0
    m_Right: 0
    m_Top: 0
    m_Bottom: 10
  m_ChildAlignment: 0
  m_Spacing: 10
  m_ChildForceExpandWidth: 1
  m_ChildForceExpandHeight: 1
  m_ChildControlWidth: 1
  m_ChildControlHeight: 1
  m_ChildScaleWidth: 0
  m_ChildScaleHeight: 0
  m_ReverseArrangement: 0
--- !u!1001 &2143050077
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 1395715298}
    m_Modifications:
    - target: {fileID: 3022974012137700811, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_Enabled
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3022974012137700811, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_AdditionalShaderChannelsFlag
      value: 25
      objectReference: {fileID: 0}
    - target: {fileID: 3097647832025268333, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_text
      value: Indicators are UI elements that ask the user to wait temporarily while
        the game is saving or loading. They're usually animated, but don't require
        any connection to game logic and are completely self-contained. These illustrate
        how these examples elements can be made in Shader Graph with zero texture
        memory.
      objectReference: {fileID: 0}
    - target: {fileID: 7513160448566667922, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_text
      value: Indicators
      objectReference: {fileID: 0}
    - target: {fileID: 7513160448566667922, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_fontSize
      value: 54
      objectReference: {fileID: 0}
    - target: {fileID: 7513160448566667922, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_fontStyle
      value: 17
      objectReference: {fileID: 0}
    - target: {fileID: 7513160448566667922, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_fontSizeBase
      value: 54
      objectReference: {fileID: 0}
    - target: {fileID: 8208681277822599831, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_Name
      value: Page - Indicators
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_Pivot.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_Pivot.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_AnchorMax.x
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_SizeDelta.x
      value: -420
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_SizeDelta.y
      value: -40
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 190
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8405060111705126841, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_text
      value: These UI elements indicate loading, saving, or other situations where
        the user needs to wait. They are self-contained and don't require passing
        any data in from outside the element itself.
      objectReference: {fileID: 0}
    - target: {fileID: 8405060111705126841, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_fontSize
      value: 30
      objectReference: {fileID: 0}
    - target: {fileID: 8405060111705126841, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_fontSizeBase
      value: 30
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects:
    - targetCorrespondingSourceObject: {fileID: 4262581316619277117, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      insertIndex: -1
      addedObject: {fileID: 1900708143}
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
--- !u!224 &2143050078 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
  m_PrefabInstance: {fileID: 2143050077}
  m_PrefabAsset: {fileID: 0}
--- !u!224 &2143050079 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 4262581316619277117, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
  m_PrefabInstance: {fileID: 2143050077}
  m_PrefabAsset: {fileID: 0}
--- !u!223 &2143050080 stripped
Canvas:
  m_CorrespondingSourceObject: {fileID: 3022974012137700811, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
  m_PrefabInstance: {fileID: 2143050077}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &1112707155501840389
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 1395715298}
    m_Modifications:
    - target: {fileID: 3022974012137700811, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_Enabled
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3022974012137700811, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_AdditionalShaderChannelsFlag
      value: 25
      objectReference: {fileID: 0}
    - target: {fileID: 3097647832025268333, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_text
      value: Many UI elements such as these rounded rectangles need to adapt to changing
        aspect ratios. These samples show how you can use the included RectTransform
        Size node to pass in the width and height of the UI element so that the shader
        can adapt to fit.
      objectReference: {fileID: 0}
    - target: {fileID: 4262581316619277117, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_SizeDelta.x
      value: -100
      objectReference: {fileID: 0}
    - target: {fileID: 4262581316619277117, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7513160448566667922, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_fontSize
      value: 54
      objectReference: {fileID: 0}
    - target: {fileID: 7513160448566667922, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_fontStyle
      value: 17
      objectReference: {fileID: 0}
    - target: {fileID: 7513160448566667922, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_fontSizeBase
      value: 54
      objectReference: {fileID: 0}
    - target: {fileID: 7661986276067569678, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_SizeDelta.x
      value: -100
      objectReference: {fileID: 0}
    - target: {fileID: 7661986276067569678, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8208681277822599831, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_Name
      value: Page - RectTransform Size
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_Pivot.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_Pivot.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_AnchorMax.x
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_SizeDelta.x
      value: -420
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_SizeDelta.y
      value: -40
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 190
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8212681213235755239, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8405060111705126841, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_text
      value: 'Some shapes, such as the Rounded Rectangle, can adapt to any aspect
        ratio if you pass the Rect Transform Size to the shader.

        The ImageSize
        component sets the Shader Graph''s _RectTransformSize Property (Vector2),
        which is then passed to the shader''s Rectangles for proper rendering.'
      objectReference: {fileID: 0}
    - target: {fileID: 8405060111705126841, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_fontSize
      value: 30
      objectReference: {fileID: 0}
    - target: {fileID: 8405060111705126841, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      propertyPath: m_fontSizeBase
      value: 30
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects:
    - targetCorrespondingSourceObject: {fileID: 4262581316619277117, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      insertIndex: -1
      addedObject: {fileID: 510766279}
    - targetCorrespondingSourceObject: {fileID: 4262581316619277117, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      insertIndex: -1
      addedObject: {fileID: 1501147054}
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: 5597060975141118542, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
      insertIndex: -1
      addedObject: {fileID: 2097437752}
  m_SourcePrefab: {fileID: 100100000, guid: abd54f4572036c549b81c1816521a3b1, type: 3}
--- !u!1001 &1773589103853002916
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 1395715298}
    m_Modifications:
    - target: {fileID: 545122118422890375, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_IsOn
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 545122118422890375, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[1].m_Target
      value: 
      objectReference: {fileID: 402657853}
    - target: {fileID: 660985845214848987, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 660985845214848987, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 660985845214848987, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 660985845214848987, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 660985845214848987, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 660985845214848987, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 660985845214848987, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1203758887085499046, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_Pivot.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1203758887085499046, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_Pivot.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 1203758887085499046, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_AnchorMax.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1203758887085499046, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1203758887085499046, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1203758887085499046, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1203758887085499046, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_SizeDelta.x
      value: 380
      objectReference: {fileID: 0}
    - target: {fileID: 1203758887085499046, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_SizeDelta.y
      value: -40
      objectReference: {fileID: 0}
    - target: {fileID: 1203758887085499046, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1203758887085499046, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1203758887085499046, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1203758887085499046, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1203758887085499046, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1203758887085499046, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1203758887085499046, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1203758887085499046, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 20
      objectReference: {fileID: 0}
    - target: {fileID: 1203758887085499046, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1203758887085499046, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1203758887085499046, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1203758887085499046, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2092777759001766158, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2092777759001766158, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2092777759001766158, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2092777759001766158, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2092777759001766158, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2092777759001766158, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2092777759001766158, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2387699207652719860, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2387699207652719860, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2387699207652719860, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2387699207652719860, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2387699207652719860, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2387699207652719860, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2387699207652719860, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3431229947254316200, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_IsOn
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 3431229947254316200, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_Colors.m_PressedColor.b
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3431229947254316200, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_Colors.m_PressedColor.g
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3431229947254316200, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_Colors.m_PressedColor.r
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3431229947254316200, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_Colors.m_SelectedColor.b
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3431229947254316200, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_Colors.m_SelectedColor.g
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3431229947254316200, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_Colors.m_SelectedColor.r
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3431229947254316200, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_Colors.m_HighlightedColor.b
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3431229947254316200, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_Colors.m_HighlightedColor.g
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3431229947254316200, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_Colors.m_HighlightedColor.r
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3431229947254316200, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[1].m_Target
      value: 
      objectReference: {fileID: 1043085074}
    - target: {fileID: 3713820456826326923, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_Name
      value: Side Panel
      objectReference: {fileID: 0}
    - target: {fileID: 4303589813462913661, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_IsOn
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4303589813462913661, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_Colors.m_PressedColor.b
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4303589813462913661, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_Colors.m_PressedColor.g
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4303589813462913661, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_Colors.m_PressedColor.r
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4303589813462913661, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_Colors.m_SelectedColor.b
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4303589813462913661, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_Colors.m_SelectedColor.g
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4303589813462913661, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_Colors.m_SelectedColor.r
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4303589813462913661, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_Colors.m_HighlightedColor.b
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4303589813462913661, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_Colors.m_HighlightedColor.g
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4303589813462913661, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_Colors.m_HighlightedColor.r
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4303589813462913661, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[1].m_Target
      value: 
      objectReference: {fileID: 883955029}
    - target: {fileID: 4303589813462913661, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[1].m_MethodName
      value: set_enabled
      objectReference: {fileID: 0}
    - target: {fileID: 4681715654555475155, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4681715654555475155, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4681715654555475155, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4681715654555475155, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4681715654555475155, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4681715654555475155, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4681715654555475155, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4780546769034199962, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_IsOn
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4780546769034199962, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_Colors.m_PressedColor.b
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4780546769034199962, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_Colors.m_PressedColor.g
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4780546769034199962, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_Colors.m_PressedColor.r
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4780546769034199962, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_Colors.m_SelectedColor.b
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4780546769034199962, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_Colors.m_SelectedColor.g
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4780546769034199962, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_Colors.m_SelectedColor.r
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4780546769034199962, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_Colors.m_HighlightedColor.b
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4780546769034199962, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_Colors.m_HighlightedColor.g
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4780546769034199962, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_Colors.m_HighlightedColor.r
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4780546769034199962, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[1].m_Target
      value: 
      objectReference: {fileID: 471393354}
    - target: {fileID: 6257368374417626478, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_IsOn
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6257368374417626478, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[1].m_Target
      value: 
      objectReference: {fileID: 2143050080}
    - target: {fileID: 7270375089428625641, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7270375089428625641, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7270375089428625641, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7270375089428625641, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7270375089428625641, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7270375089428625641, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7270375089428625641, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7370895053767255968, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_IsOn
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7370895053767255968, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_Colors.m_PressedColor.b
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7370895053767255968, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_Colors.m_PressedColor.g
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7370895053767255968, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_Colors.m_PressedColor.r
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7370895053767255968, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_Colors.m_SelectedColor.b
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7370895053767255968, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_Colors.m_SelectedColor.g
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7370895053767255968, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_Colors.m_SelectedColor.r
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7370895053767255968, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_Colors.m_HighlightedColor.b
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7370895053767255968, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_Colors.m_HighlightedColor.g
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7370895053767255968, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_Colors.m_HighlightedColor.r
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7370895053767255968, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[1].m_Target
      value: 
      objectReference: {fileID: 1404563811}
    - target: {fileID: 8098814671772724765, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8098814671772724765, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8098814671772724765, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8098814671772724765, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8098814671772724765, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8098814671772724765, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8098814671772724765, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9203259170453009910, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9203259170453009910, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 602277c685fc9544cb05fff8764f5a78, type: 3}
--- !u!1001 &2236064079864338391
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 1649024369}
    m_Modifications:
    - target: {fileID: 92038933656319273, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_Name
      value: Saturation Slider
      objectReference: {fileID: 0}
    - target: {fileID: 905015512013034897, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_text
      value: Saturation
      objectReference: {fileID: 0}
    - target: {fileID: 905015512013034897, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_fontStyle
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 905015512013034897, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_fontColor.b
      value: 0.72955966
      objectReference: {fileID: 0}
    - target: {fileID: 905015512013034897, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_fontColor.g
      value: 0.72955966
      objectReference: {fileID: 0}
    - target: {fileID: 905015512013034897, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_fontColor.r
      value: 0.72955966
      objectReference: {fileID: 0}
    - target: {fileID: 905015512013034897, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_fontColor32.rgba
      value: 4290427578
      objectReference: {fileID: 0}
    - target: {fileID: 7194105814776850979, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7194105814776850979, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_Pivot.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_Pivot.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_AnchorMax.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8561477015106430877, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8561477015106430877, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
--- !u!224 &2236064079864338392 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 8467773821183713189, guid: 368d1e25fe01b4143aba083adeb1a19a, type: 3}
  m_PrefabInstance: {fileID: 2236064079864338391}
  m_PrefabAsset: {fileID: 0}
--- !u!1660057539 &9223372036854775807
SceneRoots:
  m_ObjectHideFlags: 0
  m_Roots:
  - {fileID: 1084581137}
  - {fileID: 1402949191}
  - {fileID: 1395715298}
  - {fileID: 1084802480}
