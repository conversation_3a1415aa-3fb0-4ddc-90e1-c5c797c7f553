using UnityEngine;
using UnityEngine.UI;
using TMPro;

public class MenuUIBuilder : MonoBehaviour
{
    [Head<PERSON>("UI Prefab References")]
    public GameObject buttonPrefab;
    public GameObject titlePrefab;
    
    [Header("Menu Configuration")]
    public string gameTitle = "Pornstar Collection";
    public string[] buttonTexts = { "PLAY", "OPTIONS", "QUIT" };
    public Color titleColor = Color.white;
    public Color buttonColor = new Color(0.2f, 0.6f, 1f, 1f);
    
    [Header("Layout Settings")]
    public float titleYPosition = -150f;
    public float buttonSpacing = 80f;
    public float firstButtonYPosition = 50f;
    
    private Canvas canvas;
    private MainMenuManager menuManager;
    
    void Start()
    {
        canvas = FindObjectOfType<Canvas>();
        menuManager = FindObjectOfType<MainMenuManager>();
        
        if (canvas == null)
        {
            Debug.LogError("No Canvas found in scene!");
            return;
        }
        
        BuildUI();
    }
    
    void BuildUI()
    {
        // Create title
        CreateTitle();
        
        // Create buttons
        CreateButtons();
        
        // Setup menu manager references
        SetupMenuManagerReferences();
    }
    
    void CreateTitle()
    {
        GameObject titleObj = new GameObject("GameTitle");
        titleObj.transform.SetParent(canvas.transform, false);
        titleObj.layer = 5; // UI layer
        
        // Add RectTransform
        RectTransform titleRect = titleObj.AddComponent<RectTransform>();
        titleRect.anchorMin = new Vector2(0.5f, 1f);
        titleRect.anchorMax = new Vector2(0.5f, 1f);
        titleRect.anchoredPosition = new Vector2(0, titleYPosition);
        titleRect.sizeDelta = new Vector2(800, 100);
        titleRect.pivot = new Vector2(0.5f, 0.5f);
        
        // Add TextMeshPro component
        TextMeshProUGUI titleText = titleObj.AddComponent<TextMeshProUGUI>();
        titleText.text = gameTitle;
        titleText.fontSize = 72;
        titleText.fontStyle = FontStyles.Bold;
        titleText.color = titleColor;
        titleText.alignment = TextAlignmentOptions.Center;
        
        // Add gradient effect
        titleText.enableVertexGradient = true;
        titleText.colorGradient = new VertexGradient(
            new Color(1f, 0.8f, 0.2f, 1f), // top left
            new Color(1f, 0.8f, 0.2f, 1f), // top right
            new Color(1f, 0.4f, 0.8f, 1f), // bottom left
            new Color(1f, 0.4f, 0.8f, 1f)  // bottom right
        );
        
        // Store reference for menu manager
        if (menuManager != null)
        {
            menuManager.gameTitle = titleText;
        }
    }
    
    void CreateButtons()
    {
        Button[] buttons = new Button[buttonTexts.Length];
        
        for (int i = 0; i < buttonTexts.Length; i++)
        {
            buttons[i] = CreateButton(buttonTexts[i], i);
        }
        
        // Store references for menu manager
        if (menuManager != null && buttons.Length >= 3)
        {
            menuManager.playButton = buttons[0];
            menuManager.optionsButton = buttons[1];
            menuManager.quitButton = buttons[2];
        }
    }
    
    Button CreateButton(string text, int index)
    {
        // Create button GameObject
        GameObject buttonObj = new GameObject($"{text}Button");
        buttonObj.transform.SetParent(canvas.transform, false);
        buttonObj.layer = 5; // UI layer
        
        // Add RectTransform
        RectTransform buttonRect = buttonObj.AddComponent<RectTransform>();
        buttonRect.anchorMin = new Vector2(0.5f, 0.5f);
        buttonRect.anchorMax = new Vector2(0.5f, 0.5f);
        buttonRect.anchoredPosition = new Vector2(0, firstButtonYPosition - (index * buttonSpacing));
        buttonRect.sizeDelta = new Vector2(200, 60);
        buttonRect.pivot = new Vector2(0.5f, 0.5f);
        
        // Add Image component for button background
        Image buttonImage = buttonObj.AddComponent<Image>();
        buttonImage.color = buttonColor;
        buttonImage.sprite = Resources.GetBuiltinResource<Sprite>("UI/Skin/UISprite.psd");
        buttonImage.type = Image.Type.Sliced;
        
        // Add Button component
        Button button = buttonObj.AddComponent<Button>();
        button.targetGraphic = buttonImage;

        // Add hover effect
        ButtonHoverEffect hoverEffect = buttonObj.AddComponent<ButtonHoverEffect>();
        hoverEffect.hoverScale = 1.05f;
        hoverEffect.animationDuration = 0.15f;
        
        // Setup button colors
        ColorBlock colors = button.colors;
        colors.normalColor = Color.white;
        colors.highlightedColor = new Color(0.96f, 0.96f, 0.96f, 1f);
        colors.pressedColor = new Color(0.78f, 0.78f, 0.78f, 1f);
        colors.selectedColor = new Color(0.96f, 0.96f, 0.96f, 1f);
        colors.disabledColor = new Color(0.78f, 0.78f, 0.78f, 0.5f);
        colors.colorMultiplier = 1f;
        colors.fadeDuration = 0.1f;
        button.colors = colors;
        
        // Create button text
        CreateButtonText(buttonObj, text);
        
        return button;
    }
    
    void CreateButtonText(GameObject buttonParent, string text)
    {
        GameObject textObj = new GameObject("Text (TMP)");
        textObj.transform.SetParent(buttonParent.transform, false);
        textObj.layer = 5; // UI layer
        
        // Add RectTransform
        RectTransform textRect = textObj.AddComponent<RectTransform>();
        textRect.anchorMin = Vector2.zero;
        textRect.anchorMax = Vector2.one;
        textRect.anchoredPosition = Vector2.zero;
        textRect.sizeDelta = Vector2.zero;
        textRect.pivot = new Vector2(0.5f, 0.5f);
        
        // Add TextMeshPro component
        TextMeshProUGUI buttonText = textObj.AddComponent<TextMeshProUGUI>();
        buttonText.text = text;
        buttonText.fontSize = 24;
        buttonText.fontStyle = FontStyles.Bold;
        buttonText.color = Color.white;
        buttonText.alignment = TextAlignmentOptions.Center;
        buttonText.raycastTarget = false; // Prevent text from blocking button clicks
    }
    
    void SetupMenuManagerReferences()
    {
        if (menuManager == null)
        {
            Debug.LogWarning("MainMenuManager not found. Button functionality may not work.");
        }
    }
}
