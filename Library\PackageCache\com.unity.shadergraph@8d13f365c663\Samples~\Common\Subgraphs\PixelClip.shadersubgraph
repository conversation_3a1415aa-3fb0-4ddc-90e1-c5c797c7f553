{
    "m_SGVersion": 3,
    "m_Type": "UnityEditor.ShaderGraph.GraphData",
    "m_ObjectId": "92238c2348bf47838ab830371098566e",
    "m_Properties": [
        {
            "m_Id": "7c32cb4bbe6d4618a6170ff7c4f54e15"
        },
        {
            "m_Id": "5f6a63b68a254614b1723c51adad6966"
        }
    ],
    "m_Keywords": [],
    "m_Dropdowns": [],
    "m_CategoryData": [
        {
            "m_Id": "1ef1c455cc3741f0ab122e23e7b214f8"
        }
    ],
    "m_Nodes": [
        {
            "m_Id": "342adc6d6dcd456bbea6066ccdaa4c65"
        },
        {
            "m_Id": "a2e130a860e84199be89f3ab1f5b0406"
        },
        {
            "m_Id": "a59e8d5638664e259a049944fb1f7d39"
        },
        {
            "m_Id": "27000e196f084287a83025dcdbf48aeb"
        }
    ],
    "m_GroupDatas": [],
    "m_StickyNoteDatas": [],
    "m_Edges": [
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "27000e196f084287a83025dcdbf48aeb"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "a2e130a860e84199be89f3ab1f5b0406"
                },
                "m_SlotId": 2
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "a2e130a860e84199be89f3ab1f5b0406"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "342adc6d6dcd456bbea6066ccdaa4c65"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "a59e8d5638664e259a049944fb1f7d39"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "a2e130a860e84199be89f3ab1f5b0406"
                },
                "m_SlotId": 0
            }
        }
    ],
    "m_VertexContext": {
        "m_Position": {
            "x": 0.0,
            "y": 0.0
        },
        "m_Blocks": []
    },
    "m_FragmentContext": {
        "m_Position": {
            "x": 0.0,
            "y": 0.0
        },
        "m_Blocks": []
    },
    "m_PreviewData": {
        "serializedMesh": {
            "m_SerializedMesh": "{\"mesh\":{\"instanceID\":0}}",
            "m_Guid": ""
        },
        "preventRotation": false
    },
    "m_Path": "Sub Graphs",
    "m_GraphPrecision": 1,
    "m_PreviewMode": 2,
    "m_OutputNode": {
        "m_Id": "342adc6d6dcd456bbea6066ccdaa4c65"
    },
    "m_SubDatas": [],
    "m_ActiveTargets": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "0944166fed944ca99e741e0ea61cc32f",
    "m_Id": 1,
    "m_DisplayName": "B",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.CategoryData",
    "m_ObjectId": "1ef1c455cc3741f0ab122e23e7b214f8",
    "m_Name": "",
    "m_ChildObjectList": [
        {
            "m_Id": "7c32cb4bbe6d4618a6170ff7c4f54e15"
        },
        {
            "m_Id": "5f6a63b68a254614b1723c51adad6966"
        }
    ]
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PropertyNode",
    "m_ObjectId": "27000e196f084287a83025dcdbf48aeb",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Property",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -356.5,
            "y": 73.49999237060547,
            "width": 127.50001525878906,
            "height": 34.00000762939453
        }
    },
    "m_Slots": [
        {
            "m_Id": "e828277e5df746c9b5f83697334f227b"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Property": {
        "m_Id": "5f6a63b68a254614b1723c51adad6966"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SubGraphOutputNode",
    "m_ObjectId": "342adc6d6dcd456bbea6066ccdaa4c65",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Output",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "dae94ab4b5644002989fd50ba529dd5d"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "IsFirstSlotValid": true
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.Internal.Vector1ShaderProperty",
    "m_ObjectId": "5f6a63b68a254614b1723c51adad6966",
    "m_Guid": {
        "m_GuidSerialized": "9d94a6d6-e860-4220-8464-258d5758d374"
    },
    "m_Name": "Threshold",
    "m_DefaultRefNameVersion": 1,
    "m_RefNameGeneratedByDisplayName": "Threshold",
    "m_DefaultReferenceName": "_Threshold",
    "m_OverrideReferenceName": "",
    "m_GeneratePropertyBlock": true,
    "m_UseCustomSlotLabel": false,
    "m_CustomSlotLabel": "",
    "m_DismissedVersion": 0,
    "m_Precision": 0,
    "overrideHLSLDeclaration": false,
    "hlslDeclarationOverride": 0,
    "m_Hidden": false,
    "m_Value": 0.03999999910593033,
    "m_FloatType": 0,
    "m_RangeValues": {
        "x": 0.0,
        "y": 1.0
    }
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.Internal.Vector1ShaderProperty",
    "m_ObjectId": "7c32cb4bbe6d4618a6170ff7c4f54e15",
    "m_Guid": {
        "m_GuidSerialized": "f76e9f0a-0786-4b23-a5db-649c33da273a"
    },
    "m_Name": "In",
    "m_DefaultRefNameVersion": 1,
    "m_RefNameGeneratedByDisplayName": "In",
    "m_DefaultReferenceName": "_In",
    "m_OverrideReferenceName": "",
    "m_GeneratePropertyBlock": true,
    "m_UseCustomSlotLabel": false,
    "m_CustomSlotLabel": "",
    "m_DismissedVersion": 0,
    "m_Precision": 0,
    "overrideHLSLDeclaration": false,
    "hlslDeclarationOverride": 0,
    "m_Hidden": false,
    "m_Value": 0.0,
    "m_FloatType": 0,
    "m_RangeValues": {
        "x": 0.0,
        "y": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "8adf2c99ad734f9586c35b044535f8eb",
    "m_Id": 2,
    "m_DisplayName": "Threshold",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Threshold",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.CustomFunctionNode",
    "m_ObjectId": "a2e130a860e84199be89f3ab1f5b0406",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "clipFunction (Custom Function)",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -229.0,
            "y": 0.0,
            "width": 222.5,
            "height": 94.00003051757813
        }
    },
    "m_Slots": [
        {
            "m_Id": "f5c7434911f441fdbaf928673b394c6f"
        },
        {
            "m_Id": "8adf2c99ad734f9586c35b044535f8eb"
        },
        {
            "m_Id": "0944166fed944ca99e741e0ea61cc32f"
        }
    ],
    "synonyms": [
        "code",
        "HLSL"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SourceType": 1,
    "m_FunctionName": "clipFunction",
    "m_FunctionSource": "",
    "m_FunctionBody": "clip(A - Threshold);\nB = A;"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PropertyNode",
    "m_ObjectId": "a59e8d5638664e259a049944fb1f7d39",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Property",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -313.50006103515627,
            "y": 39.50000762939453,
            "width": 84.50003051757813,
            "height": 34.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "ee767a14b8c04d7996e05d4c3c619b4f"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Property": {
        "m_Id": "7c32cb4bbe6d4618a6170ff7c4f54e15"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "dae94ab4b5644002989fd50ba529dd5d",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "e828277e5df746c9b5f83697334f227b",
    "m_Id": 0,
    "m_DisplayName": "Threshold",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "ee767a14b8c04d7996e05d4c3c619b4f",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "f5c7434911f441fdbaf928673b394c6f",
    "m_Id": 0,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

