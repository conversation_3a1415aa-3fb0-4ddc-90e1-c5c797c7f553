using UnityEngine;
using UnityEngine.UI;

public class ResponsiveUIManager : MonoBehaviour
{
    [Header("Screen Size Breakpoints")]
    public int mobileMaxWidth = 768;
    public int tabletMaxWidth = 1024;
    
    [Header("UI Scale Factors")]
    public float mobileScaleFactor = 0.8f;
    public float tabletScaleFactor = 0.9f;
    public float desktopScaleFactor = 1.0f;
    
    [Header("Layout Adjustments")]
    public bool adjustButtonSpacing = true;
    public float mobileButtonSpacing = 60f;
    public float tabletButtonSpacing = 70f;
    public float desktopButtonSpacing = 80f;
    
    [Header("Font Size Adjustments")]
    public bool adjustFontSizes = true;
    public float mobileFontScale = 0.8f;
    public float tabletFontScale = 0.9f;
    public float desktopFontScale = 1.0f;
    
    private CanvasScaler canvasScaler;
    private MenuUIBuilder menuBuilder;
    private ScreenOrientation lastOrientation;
    private Vector2 lastScreenSize;
    
    public enum DeviceType
    {
        Mobile,
        Tablet,
        Desktop
    }
    
    void Start()
    {
        canvasScaler = GetComponent<CanvasScaler>();
        menuBuilder = GetComponent<MenuUIBuilder>();
        
        lastOrientation = Screen.orientation;
        lastScreenSize = new Vector2(Screen.width, Screen.height);
        
        ApplyResponsiveSettings();
    }
    
    void Update()
    {
        // Check for screen size or orientation changes
        if (Screen.orientation != lastOrientation || 
            new Vector2(Screen.width, Screen.height) != lastScreenSize)
        {
            lastOrientation = Screen.orientation;
            lastScreenSize = new Vector2(Screen.width, Screen.height);
            
            // Delay to ensure screen change is complete
            Invoke(nameof(ApplyResponsiveSettings), 0.1f);
        }
    }
    
    void ApplyResponsiveSettings()
    {
        DeviceType deviceType = GetDeviceType();
        
        ApplyCanvasScaling(deviceType);
        
        if (adjustButtonSpacing)
        {
            ApplyButtonSpacing(deviceType);
        }
        
        if (adjustFontSizes)
        {
            ApplyFontScaling(deviceType);
        }
        
        Debug.Log($"Applied responsive settings for {deviceType} device");
    }
    
    DeviceType GetDeviceType()
    {
        int screenWidth = Screen.width;
        
        if (screenWidth <= mobileMaxWidth)
        {
            return DeviceType.Mobile;
        }
        else if (screenWidth <= tabletMaxWidth)
        {
            return DeviceType.Tablet;
        }
        else
        {
            return DeviceType.Desktop;
        }
    }
    
    void ApplyCanvasScaling(DeviceType deviceType)
    {
        if (canvasScaler == null) return;
        
        float scaleFactor = desktopScaleFactor;
        
        switch (deviceType)
        {
            case DeviceType.Mobile:
                scaleFactor = mobileScaleFactor;
                break;
            case DeviceType.Tablet:
                scaleFactor = tabletScaleFactor;
                break;
            case DeviceType.Desktop:
                scaleFactor = desktopScaleFactor;
                break;
        }
        
        canvasScaler.scaleFactor = scaleFactor;
    }
    
    void ApplyButtonSpacing(DeviceType deviceType)
    {
        if (menuBuilder == null) return;
        
        float spacing = desktopButtonSpacing;
        
        switch (deviceType)
        {
            case DeviceType.Mobile:
                spacing = mobileButtonSpacing;
                break;
            case DeviceType.Tablet:
                spacing = tabletButtonSpacing;
                break;
            case DeviceType.Desktop:
                spacing = desktopButtonSpacing;
                break;
        }
        
        menuBuilder.buttonSpacing = spacing;
    }
    
    void ApplyFontScaling(DeviceType deviceType)
    {
        float fontScale = desktopFontScale;
        
        switch (deviceType)
        {
            case DeviceType.Mobile:
                fontScale = mobileFontScale;
                break;
            case DeviceType.Tablet:
                fontScale = tabletFontScale;
                break;
            case DeviceType.Desktop:
                fontScale = desktopFontScale;
                break;
        }
        
        // Apply font scaling to all text components in the scene
        TMPro.TextMeshProUGUI[] textComponents = FindObjectsOfType<TMPro.TextMeshProUGUI>();
        
        foreach (var textComponent in textComponents)
        {
            // Store original font size if not already stored
            if (!textComponent.gameObject.GetComponent<OriginalFontSize>())
            {
                OriginalFontSize originalSize = textComponent.gameObject.AddComponent<OriginalFontSize>();
                originalSize.originalSize = textComponent.fontSize;
            }
            
            OriginalFontSize originalFontSize = textComponent.gameObject.GetComponent<OriginalFontSize>();
            textComponent.fontSize = originalFontSize.originalSize * fontScale;
        }
    }
    
    // Utility method to get safe area for devices with notches
    public Rect GetSafeArea()
    {
        return Screen.safeArea;
    }
    
    // Apply safe area padding for devices with notches
    public void ApplySafeArea(RectTransform rectTransform)
    {
        Rect safeArea = GetSafeArea();
        Vector2 screenSize = new Vector2(Screen.width, Screen.height);
        
        Vector2 anchorMin = safeArea.position;
        Vector2 anchorMax = safeArea.position + safeArea.size;
        
        anchorMin.x /= screenSize.x;
        anchorMin.y /= screenSize.y;
        anchorMax.x /= screenSize.x;
        anchorMax.y /= screenSize.y;
        
        rectTransform.anchorMin = anchorMin;
        rectTransform.anchorMax = anchorMax;
    }
    
    // Public method to manually trigger responsive update
    public void UpdateResponsiveSettings()
    {
        ApplyResponsiveSettings();
    }
}

// Helper component to store original font sizes
public class OriginalFontSize : MonoBehaviour
{
    public float originalSize;
}
