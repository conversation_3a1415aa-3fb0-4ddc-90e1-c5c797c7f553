%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Nettle
  m_Shader: {fileID: -6465566751694194690, guid: 7d8ecf52dc28c754e8b6abd2a553e040,
    type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords:
  - _ALPHATEST_ON
  - _DISABLE_SSR_TRANSPARENT
  m_InvalidKeywords: []
  m_LightmapFlags: 0
  m_EnableInstancingVariants: 1
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: 2475
  stringTagMap:
    MotionVector: User
    RenderType: TransparentCutout
  disabledShaderPasses:
  - MOTIONVECTORS
  - TransparentDepthPrepass
  - TransparentDepthPostpass
  - TransparentBackface
  - RayTracingPrepass
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _Nettle:
        m_Texture: {fileID: 2800000, guid: a2415de992b2e1a42935cdf120a45fb7, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SampleTexture2D_ec6e66a1435b4c4db142ede36eabd28c_Texture_1_Texture2D:
        m_Texture: {fileID: 2800000, guid: a2415de992b2e1a42935cdf120a45fb7, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - Animation_Cutoff: 30
    - Distance_Fade_End: 70
    - Distance_Fade_Start: -10
    - Vector1_593c5cea6c4a42e993ed03ced4685732: 1
    - Vector1_8651797e3e304e108dbd25f9d5a426ba: 0.35
    - Vector1_a5b8b09028ce49a39f4d090894c89e22: 0.5
    - Vector1_a6983181c8dc4691ba6a28a34c4223a6: 2
    - Wind_Blast: 0.05
    - Wind_Intensity: 0.1
    - Wind_Ripples: 0.05
    - Wind_Speed: 3
    - Wind_Turbulence: 0
    - Wind_Wavelength: 10
    - Wind_Yaw: 180
    - _AddPrecomputedVelocity: 0
    - _AlphaClip: 1
    - _AlphaCutoffEnable: 1
    - _AlphaDstBlend: 0
    - _AlphaSrcBlend: 1
    - _AlphaToMask: 1
    - _Blend: 0
    - _BlendMode: 0
    - _BlendModePreserveSpecular: 0
    - _CastShadows: 1
    - _ConservativeDepthOffsetEnable: 0
    - _Cull: 0
    - _CullMode: 2
    - _CullModeForward: 2
    - _DepthOffsetEnable: 0
    - _DoubleSidedEnable: 0
    - _DoubleSidedGIMode: 0
    - _DoubleSidedNormalMode: 2
    - _DstBlend: 0
    - _DstBlend2: 0
    - _EnableBlendModePreserveSpecularLighting: 1
    - _EnableFogOnTransparent: 1
    - _ExcludeFromTUAndAA: 0
    - _FadeBias: 1
    - _GrassNormal: 0
    - _GroundFalloff: 1
    - _MaterialID: 1
    - _MaterialTypeMask: 2
    - _OpaqueCullMode: 2
    - _PerPixelSorting: 0
    - _QueueControl: 1
    - _QueueOffset: 0
    - _RayTracing: 0
    - _ReceiveShadows: 1
    - _ReceivesSSR: 1
    - _ReceivesSSRTransparent: 0
    - _RefractionModel: 0
    - _RenderQueueType: 1
    - _RequireSplitLighting: 0
    - _SSS_Effect: 3
    - _SSS_Shadows: 1
    - _SrcBlend: 1
    - _StencilRef: 0
    - _StencilRefDepth: 8
    - _StencilRefDistortionVec: 4
    - _StencilRefGBuffer: 10
    - _StencilRefMV: 40
    - _StencilWriteMask: 6
    - _StencilWriteMaskDepth: 9
    - _StencilWriteMaskDistortionVec: 4
    - _StencilWriteMaskGBuffer: 15
    - _StencilWriteMaskMV: 41
    - _SupportDecals: 1
    - _Surface: 0
    - _SurfaceType: 0
    - _TransmissionEnable: 1
    - _TransparentBackfaceEnable: 0
    - _TransparentCullMode: 2
    - _TransparentDepthPostpassEnable: 0
    - _TransparentDepthPrepassEnable: 0
    - _TransparentSortPriority: 0
    - _TransparentWritingMotionVec: 0
    - _TransparentZWrite: 0
    - _UseShadowThreshold: 0
    - _WorkflowMode: 1
    - _ZTest: 4
    - _ZTestDepthEqualForOpaque: 3
    - _ZTestGBuffer: 3
    - _ZTestTransparent: 4
    - _ZWrite: 1
    - _ZWriteControl: 0
    m_Colors:
    - Fade_Color: {r: 0, g: 0, b: 0, a: 0}
    - _AORemap: {r: 0, g: 6, b: 0, a: 0}
    - _DoubleSidedConstants: {r: 1, g: 1, b: -1, a: 0}
    - _EmissionColor: {r: 1, g: 1, b: 1, a: 1}
    - _SSSColor: {r: 1.2221785, g: 1.4980394, b: 0, a: 0}
    - _Thickness_Remap: {r: 1, g: 1, b: 0, a: 0}
  m_BuildTextureStacks: []
  m_AllowLocking: 1
--- !u!114 &696742687155886869
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0353a89b1f911e48b9e16bdc9f2e058, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 9
--- !u!114 &6787200079116005098
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: da692e001514ec24dbc4cca1949ff7e8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 13
  hdPluginSubTargetMaterialVersions:
    m_Keys: []
    m_Values: 
