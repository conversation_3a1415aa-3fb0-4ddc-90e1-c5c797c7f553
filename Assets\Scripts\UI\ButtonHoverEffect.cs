using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;
using System.Collections;

public class ButtonHoverEffect : Mono<PERSON>eh<PERSON>our, IPointerEnterHandler, IPointerExitHandler
{
    [Header("Hover Animation Settings")]
    public float hoverScale = 1.1f;
    public float animationDuration = 0.2f;
    public AnimationCurve scaleCurve = AnimationCurve.EaseInOut(0f, 0f, 1f, 1f);
    
    [Header("Color Animation")]
    public bool animateColor = true;
    public Color hoverColor = Color.white;
    
    [Header("Sound Effects")]
    public AudioClip hoverSound;
    public AudioClip clickSound;
    
    private Vector3 originalScale;
    private Color originalColor;
    private Image buttonImage;
    private Button button;
    private AudioSource audioSource;
    private Coroutine currentAnimation;
    
    void Start()
    {
        // Store original values
        originalScale = transform.localScale;
        
        // Get components
        buttonImage = GetComponent<Image>();
        button = GetComponent<Button>();
        
        if (buttonImage != null)
        {
            originalColor = buttonImage.color;
        }
        
        // Setup audio source for sound effects
        audioSource = GetComponent<AudioSource>();
        if (audioSource == null && (hoverSound != null || clickSound != null))
        {
            audioSource = gameObject.AddComponent<AudioSource>();
            audioSource.playOnAwake = false;
            audioSource.volume = 0.5f;
        }
        
        // Add click sound to button
        if (button != null && clickSound != null)
        {
            button.onClick.AddListener(PlayClickSound);
        }
    }
    
    public void OnPointerEnter(PointerEventData eventData)
    {
        if (button != null && !button.interactable) return;
        
        PlayHoverSound();
        AnimateToHover();
    }
    
    public void OnPointerExit(PointerEventData eventData)
    {
        if (button != null && !button.interactable) return;
        
        AnimateToNormal();
    }
    
    void AnimateToHover()
    {
        if (currentAnimation != null)
        {
            StopCoroutine(currentAnimation);
        }
        
        currentAnimation = StartCoroutine(AnimateButton(hoverScale, animateColor ? hoverColor : originalColor));
    }
    
    void AnimateToNormal()
    {
        if (currentAnimation != null)
        {
            StopCoroutine(currentAnimation);
        }
        
        currentAnimation = StartCoroutine(AnimateButton(1f, originalColor));
    }
    
    IEnumerator AnimateButton(float targetScale, Color targetColor)
    {
        Vector3 startScale = transform.localScale;
        Color startColor = buttonImage != null ? buttonImage.color : Color.white;
        
        float elapsedTime = 0f;
        
        while (elapsedTime < animationDuration)
        {
            elapsedTime += Time.deltaTime;
            float progress = elapsedTime / animationDuration;
            float curveValue = scaleCurve.Evaluate(progress);
            
            // Animate scale
            Vector3 newScale = Vector3.Lerp(startScale, originalScale * targetScale, curveValue);
            transform.localScale = newScale;
            
            // Animate color
            if (buttonImage != null && animateColor)
            {
                Color newColor = Color.Lerp(startColor, targetColor, curveValue);
                buttonImage.color = newColor;
            }
            
            yield return null;
        }
        
        // Ensure final values are set
        transform.localScale = originalScale * targetScale;
        if (buttonImage != null && animateColor)
        {
            buttonImage.color = targetColor;
        }
        
        currentAnimation = null;
    }
    
    void PlayHoverSound()
    {
        if (audioSource != null && hoverSound != null)
        {
            audioSource.clip = hoverSound;
            audioSource.Play();
        }
    }
    
    void PlayClickSound()
    {
        if (audioSource != null && clickSound != null)
        {
            audioSource.clip = clickSound;
            audioSource.Play();
        }
    }
    
    // Public method to manually trigger hover effect
    public void TriggerHoverEffect()
    {
        AnimateToHover();
        Invoke(nameof(AnimateToNormal), animationDuration * 2f);
    }
    
    // Reset to original state
    public void ResetToOriginal()
    {
        if (currentAnimation != null)
        {
            StopCoroutine(currentAnimation);
            currentAnimation = null;
        }
        
        transform.localScale = originalScale;
        if (buttonImage != null)
        {
            buttonImage.color = originalColor;
        }
    }
    
    void OnDisable()
    {
        ResetToOriginal();
    }
}
