%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-3358335109842398402
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: da692e001514ec24dbc4cca1949ff7e8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 13
  hdPluginSubTargetMaterialVersions:
    m_Keys: []
    m_Values: 
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: WaterfallSplash
  m_Shader: {fileID: -6465566751694194690, guid: 64ad4a4657d614f4eb137f75a818f5ba,
    type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords:
  - _ALPHATEST_ON
  - _ENABLE_FOG_ON_TRANSPARENT
  - _SURFACE_TYPE_TRANSPARENT
  m_InvalidKeywords: []
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: 3000
  stringTagMap:
    MotionVector: User
    RenderType: Transparent
  disabledShaderPasses:
  - DepthOnly
  - SHADOWCASTER
  - MOTIONVECTORS
  - TransparentDepthPrepass
  - TransparentDepthPostpass
  - TransparentBackface
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _FlipbookTexture:
        m_Texture: {fileID: 2800000, guid: f3d4694535055af4fb95368a81816981, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AddPrecomputedVelocity: 0
    - _AlphaClip: 1
    - _AlphaClipThreshold: 0.04
    - _AlphaCutoffEnable: 0
    - _AlphaDstBlend: 10
    - _AlphaSrcBlend: 1
    - _AlphaToMask: 0
    - _BUILTIN_QueueControl: -1
    - _BUILTIN_QueueOffset: 0
    - _Blend: 0
    - _BlendMode: 0
    - _CastShadows: 0
    - _ConservativeDepthOffsetEnable: 0
    - _ConstantFlow: 1
    - _Cull: 2
    - _CullMode: 2
    - _CullModeForward: 2
    - _DebugTime: 0
    - _DepthOffsetEnable: 0
    - _DoubleSidedEnable: 0
    - _DoubleSidedGIMode: 0
    - _DoubleSidedNormalMode: 2
    - _DstBlend: 10
    - _DstBlend2: 10
    - _EnableBlendModePreserveSpecularLighting: 0
    - _EnableFogOnTransparent: 1
    - _FadeInPower: 0.5
    - _FadeOutPower: 2
    - _FlipbookSpeed: 2
    - _ManualTime: 0
    - _MatchParticlePhase: 0
    - _Opacity: 3
    - _OpaqueCullMode: 2
    - _ParticleEndSize: 5
    - _ParticleSpeed: 2
    - _ParticleSpread: 90
    - _ParticleStartSize: 0
    - _ParticleVelocityEnd: 2
    - _ParticleVelocityStart: 4
    - _PerPixelSorting: 0
    - _QueueControl: 0
    - _QueueOffset: 0
    - _RandomizeRotationDirection: 1
    - _RenderQueueType: 3
    - _Rotation: 0
    - _RotationRandomOffset: 1
    - _RotationSpeed: 100
    - _SoftEdges: 1
    - _SrcBlend: 5
    - _StencilRef: 0
    - _StencilRefDepth: 1
    - _StencilRefDistortionVec: 4
    - _StencilRefMV: 33
    - _StencilWriteMask: 6
    - _StencilWriteMaskDepth: 9
    - _StencilWriteMaskDistortionVec: 4
    - _StencilWriteMaskMV: 43
    - _Surface: 1
    - _SurfaceType: 1
    - _TransparentBackfaceEnable: 0
    - _TransparentCullMode: 2
    - _TransparentDepthPostpassEnable: 0
    - _TransparentDepthPrepassEnable: 0
    - _TransparentSortPriority: 0
    - _TransparentWritingMotionVec: 0
    - _TransparentZWrite: 0
    - _UseShadowThreshold: 0
    - _ZTest: 4
    - _ZTestDepthEqualForOpaque: 4
    - _ZTestTransparent: 4
    - _ZWrite: 0
    - _ZWriteControl: 0
    m_Colors:
    - _DoubleSidedConstants: {r: 1, g: 1, b: -1, a: 0}
    - _EmissionColor: {r: 1, g: 1, b: 1, a: 1}
    - _EmitterDimensions: {r: 3, g: 0, b: 3, a: 0}
    - _EndColor: {r: 0.8915095, g: 1, b: 0.9907861, a: 1}
    - _FlipbookDimensions: {r: 8, g: 4, b: 0, a: 0}
    - _Gravity: {r: 0, g: -2, b: 0, a: 0}
    - _ParticleDirection: {r: 0, g: 1, b: 0, a: 0}
    - _StartColor: {r: 1, g: 1, b: 1, a: 1}
    - _Wind: {r: 0, g: 0, b: 0, a: 0}
  m_BuildTextureStacks: []
  m_AllowLocking: 1
--- !u!114 &2174680140971601413
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0353a89b1f911e48b9e16bdc9f2e058, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 9
--- !u!114 &7435706493937225041
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 639247ca83abc874e893eb93af2b5e44, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 0
