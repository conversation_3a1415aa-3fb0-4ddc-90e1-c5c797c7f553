<?xml version="1.0" encoding="utf-8"?>
<UXML xmlns:ui="UnityEngine.UIElements" xmlns:eui="UnityEditor.UIElements" xmlns:aui="UnityEditor.U2D.Animation">
  <aui:GenerateWeightsPanel name="GenerateWeightsPanel" picking-mode="Ignore">
    <ui:PopupWindow text="Weights">
      <ui:VisualElement class="form-row" name ="AssociateBonesControl">
        <ui:Label name="AssociateBonesLabel" tooltip="Automatically associate Bones to Sprites if Bones overlay on the Sprite" text="Associate Bones" />
        <ui:Toggle name="AssociateBonesField" class="form-editor" value="false" />
      </ui:VisualElement>
      <ui:VisualElement name="Content">
        <ui:VisualElement class="form-row-space" />
        <ui:VisualElement class="form-row">
          <ui:Button name="GenerateWeightsButton" text="Generate" tooltip="Generate Weights"/>
          <ui:Button name="NormalizeWeightsButton" text="Normalize" tooltip="Normalize Weights"/>
          <ui:Button name="ClearWeightsButton" text="Clear" tooltip="Clear Weights"/>
        </ui:VisualElement>
      </ui:VisualElement>
    </ui:PopupWindow>
  </aui:GenerateWeightsPanel>
</UXML>
